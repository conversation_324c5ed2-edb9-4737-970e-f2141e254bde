package migrations

import (
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"

	"pxpat-backend/internal/user-cluster/complaint-service/model"
)

// AutoMigrate 自动迁移数据库表结构
func AutoMigrate(db *gorm.DB) {
	log.Info().Msg("开始数据库迁移...")

	// 迁移投诉相关表
	err := db.AutoMigrate(
		&model.Complaint{},
		&model.ComplaintEvidence{},
		&model.ViolationCategory{},
	)
	if err != nil {
		log.Fatal().Err(err).Msg("投诉相关表迁移失败")
	}

	// 迁移身份认证相关表
	err = db.AutoMigrate(
		&model.IdentityVerification{},
		&model.Country{},
		&model.TrademarkCategory{},
	)
	if err != nil {
		log.Fatal().Err(err).Msg("身份认证相关表迁移失败")
	}

	// 迁移权益认证相关表
	err = db.AutoMigrate(
		&model.RightsVerification{},
		&model.Copyright{},
		&model.Trademark{},
		&model.PersonalityRight{},
		&model.RightsEvidence{},
	)
	if err != nil {
		log.Fatal().Err(err).Msg("权益认证相关表迁移失败")
	}

	log.Info().Msg("数据库迁移完成")
}

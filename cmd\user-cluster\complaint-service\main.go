package main

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"

	"pxpat-backend/internal/user-cluster/complaint-service/external/handler"
	"pxpat-backend/internal/user-cluster/complaint-service/external/service"
	"pxpat-backend/internal/user-cluster/complaint-service/repository/impl"
	"pxpat-backend/internal/user-cluster/complaint-service/routes/complaint"
	"pxpat-backend/internal/user-cluster/complaint-service/routes/identity"
	"pxpat-backend/internal/user-cluster/complaint-service/routes/rights"
	"pxpat-backend/internal/user-cluster/complaint-service/types"
	"pxpat-backend/pkg/cache"
	"pxpat-backend/pkg/config"
	"pxpat-backend/pkg/database"
	"pxpat-backend/pkg/middleware"
	"pxpat-backend/pkg/redis_client"
)

func main() {
	// 初始化日志
	zerolog.TimeFieldFormat = time.RFC3339
	log.Logger = log.Output(zerolog.ConsoleWriter{Out: os.Stderr})

	log.Info().Msg("投诉服务启动中...")

	// 加载配置
	cfg := &types.Config{}
	if err := config.LoadConfig("config/complaint-service.yaml", cfg); err != nil {
		log.Fatal().Err(err).Msg("加载配置失败")
	}

	// 初始化数据库连接
	db, err := database.NewGormDB(&cfg.Database)
	if err != nil {
		log.Fatal().Err(err).Msg("初始化数据库连接失败")
	}

	// 初始化Redis连接
	rdb, err := redis_client.NewRedisClient(&cfg.Redis)
	if err != nil {
		log.Fatal().Err(err).Msg("初始化Redis连接失败")
	}

	// 初始化缓存管理器
	cacheManager := cache.NewManager(rdb)

	// 初始化依赖注入
	app := initializeApp(db, rdb, cacheManager, cfg)

	// 创建HTTP服务器
	server := &http.Server{
		Addr:    fmt.Sprintf(":%d", cfg.Server.Port),
		Handler: app.engine,
	}

	// 启动服务器
	go func() {
		log.Info().
			Int("port", cfg.Server.Port).
			Msg("投诉服务启动成功")
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatal().Err(err).Msg("启动HTTP服务器失败")
		}
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Info().Msg("投诉服务关闭中...")

	// 优雅关闭
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		log.Error().Err(err).Msg("服务器关闭失败")
	}

	log.Info().Msg("投诉服务已关闭")
}

// App 应用程序结构体
type App struct {
	engine *gin.Engine
	config *types.Config
}

// initializeApp 初始化应用程序
func initializeApp(db *gorm.DB, rdb *redis.Client, cacheManager cache.Manager, cfg *types.Config) *App {
	// 初始化Repository层
	complaintRepo := impl.NewComplaintRepository(db, rdb, cacheManager)
	complaintEvidenceRepo := impl.NewComplaintEvidenceRepository(db, rdb, cacheManager)
	identityRepo := impl.NewIdentityVerificationRepository(db, rdb, cacheManager)
	countryRepo := impl.NewCountryRepository(db, rdb, cacheManager)
	trademarkCatRepo := impl.NewTrademarkCategoryRepository(db, rdb, cacheManager)

	// 初始化用户服务客户端（这里需要实现具体的客户端）
	userServiceClient := NewUserServiceClient(cfg)

	// 初始化Service层
	complaintService := service.NewComplaintService(
		complaintRepo,
		complaintEvidenceRepo,
		nil, // violationCategoryRepo 需要实现
		userServiceClient,
		cfg,
	)

	identityService := service.NewIdentityService(
		identityRepo,
		countryRepo,
		trademarkCatRepo,
		cfg,
	)

	rightsService := service.NewRightsService(
		nil, // rightsRepo 需要实现
		nil, // copyrightRepo 需要实现
		nil, // trademarkRepo 需要实现
		nil, // personalityRepo 需要实现
		nil, // rightsEvidenceRepo 需要实现
		countryRepo,
		trademarkCatRepo,
		cfg,
	)

	// 初始化Handler层
	complaintHandler := handler.NewComplaintHandler(complaintService)
	identityHandler := handler.NewIdentityHandler(identityService)
	rightsHandler := handler.NewRightsHandler(rightsService)

	// 初始化Gin引擎
	if cfg.Server.Mode == "release" {
		gin.SetMode(gin.ReleaseMode)
	}

	engine := gin.New()

	// 添加中间件
	engine.Use(gin.Logger())
	engine.Use(gin.Recovery())
	engine.Use(middleware.CORS(&cfg.Security.Cors))

	// 健康检查
	engine.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":  "ok",
			"service": "complaint-service",
			"time":    time.Now().Format(time.RFC3339),
		})
	})

	// API路由组
	apiV1 := engine.Group("/api/v1")

	// JWT认证中间件（这里需要实现具体的JWT中间件）
	authMiddleware := middleware.JWTAuth(&cfg.JWT)

	// 注册路由
	complaint.RegisterComplaintRoutes(apiV1, complaintHandler, authMiddleware)
	identity.RegisterIdentityRoutes(apiV1, identityHandler, authMiddleware)
	rights.RegisterRightsRoutes(apiV1, rightsHandler, authMiddleware)

	return &App{
		engine: engine,
		config: cfg,
	}
}

// UserServiceClient 用户服务客户端实现（简化版）
type UserServiceClient struct {
	config *types.Config
}

func NewUserServiceClient(config *types.Config) *UserServiceClient {
	return &UserServiceClient{
		config: config,
	}
}

func (c *UserServiceClient) BlockUser(ctx context.Context, blockerKSUID, blockedKSUID string) error {
	// TODO: 实现调用用户服务的拉黑接口
	log.Info().
		Str("blocker_ksuid", blockerKSUID).
		Str("blocked_ksuid", blockedKSUID).
		Msg("调用用户服务拉黑用户")
	return nil
}

func (c *UserServiceClient) GetUserInfo(ctx context.Context, userKSUID string) (*service.UserInfo, error) {
	// TODO: 实现调用用户服务获取用户信息接口
	return &service.UserInfo{
		UserKSUID: userKSUID,
		Nickname:  "测试用户",
		Email:     "<EMAIL>",
	}, nil
}

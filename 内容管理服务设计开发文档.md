# 内容管理服务设计开发文档

## 1. 项目概述

### 1.1 服务定位
内容管理服务（content-management-service）是一个独立的微服务，专门负责跨内容类型的统一管理功能。该服务通过API调用的方式与各内容服务（如video-service）进行交互，为管理员和内容创作者提供统一的内容管理界面。

### 1.2 核心职责
- 跨内容类型的统一查询和管理
- 批量内容操作（状态更新、删除等）
- 内容统计分析和报表
- 权限控制和审核流程管理
- 内容生命周期管理

### 1.3 技术架构
- **架构模式**: 微服务架构，基于API调用
- **通信方式**: HTTP REST API
- **数据存储**: PostgreSQL（管理元数据和操作日志）
- **缓存**: Redis（提升查询性能）
- **认证**: JWT Token
- **框架**: Gin + GORM

## 2. 服务架构设计

### 2.1 目录结构
```
internal/content-cluster/content-management-service/
├── client/                          # 外部服务客户端
│   ├── video_service_client.go      # 视频服务客户端
│   ├── novel_service_client.go      # 小说服务客户端（预留）
│   ├── music_service_client.go      # 音乐服务客户端（预留）
│   └── base_client.go               # 基础客户端封装
├── dto/                             # 数据传输对象
│   ├── content_dto.go               # 内容相关DTO
│   ├── management_dto.go            # 管理相关DTO
│   ├── stats_dto.go                 # 统计相关DTO
│   └── common_dto.go                # 通用DTO
├── external/                        # 外部API层
│   ├── handler/                     # 外部API处理器
│   │   ├── content_handler.go       # 内容管理处理器
│   │   ├── stats_handler.go         # 统计分析处理器
│   │   └── batch_handler.go         # 批量操作处理器
│   └── service/                     # 外部业务服务
│       ├── content_service.go       # 内容管理服务
│       ├── stats_service.go         # 统计分析服务
│       └── batch_service.go         # 批量操作服务
├── intra/                           # 内部API层
│   ├── handler/                     # 内部API处理器
│   └── service/                     # 内部业务服务
├── middleware/                      # 中间件
│   ├── auth.go                      # 认证中间件
│   ├── permission.go                # 权限中间件
│   └── rate_limit.go                # 限流中间件
├── model/                           # 数据模型
│   ├── operation_log.go             # 操作日志模型
│   ├── content_cache.go             # 内容缓存模型
│   └── management_config.go         # 管理配置模型
├── repository/                      # 数据访问层
│   ├── operation_log_repo.go        # 操作日志仓库
│   ├── content_cache_repo.go        # 内容缓存仓库
│   └── config_repo.go               # 配置仓库
├── types/                           # 类型定义
│   ├── content_types.go             # 内容类型定义
│   ├── management_types.go          # 管理类型定义
│   └── error_types.go               # 错误类型定义
├── utils/                           # 工具函数
│   ├── aggregator.go                # 数据聚合工具
│   ├── validator.go                 # 验证工具
│   └── converter.go                 # 转换工具
├── config/                          # 配置
│   └── config.go                    # 配置结构
├── migrations/                      # 数据库迁移
│   └── migrate.go                   # 迁移脚本
└── routes/                          # 路由定义
    ├── router.go                    # 主路由
    ├── content/                     # 内容管理路由
    │   ├── router.go
    │   ├── external.go
    │   └── internal.go
    └── stats/                       # 统计分析路由
        ├── router.go
        └── external.go
```

### 2.2 服务端口分配
- **HTTP端口**: 12010
- **gRPC端口**: 22010（预留）
- **健康检查**: `/health`

## 3. 数据模型设计

### 3.1 统一内容模型
```go
// UnifiedContent 统一内容模型
type UnifiedContent struct {
    ContentKSUID    string                 `json:"content_ksuid"`
    ContentType     string                 `json:"content_type"`     // video, novel, music
    Title          string                 `json:"title"`
    Description    string                 `json:"description"`
    UserKSUID      string                 `json:"user_ksuid"`
    Status         string                 `json:"status"`           // draft, published, archived, deleted
    
    // 统计数据
    ViewCount      int64                  `json:"view_count"`
    LikeCount      int64                  `json:"like_count"`
    CommentCount   int64                  `json:"comment_count"`
    FavoriteCount  int64                  `json:"favorite_count"`
    
    // 分类和标签
    CategoryID     uint                   `json:"category_id"`
    CategoryName   string                 `json:"category_name"`
    Tags           []string               `json:"tags"`
    
    // 时间信息
    CreatedAt      time.Time              `json:"created_at"`
    UpdatedAt      time.Time              `json:"updated_at"`
    PublishedAt    *time.Time             `json:"published_at,omitempty"`
    
    // 来源信息
    SourceService  string                 `json:"source_service"`   // video-service, novel-service
    SourceData     map[string]interface{} `json:"source_data"`      // 原始服务的特有数据
}
```

### 3.2 操作日志模型
```go
// OperationLog 操作日志模型
type OperationLog struct {
    ID             uint      `gorm:"primaryKey" json:"id"`
    OperatorKSUID  string    `gorm:"type:varchar(32);not null;index" json:"operator_ksuid"`
    OperationType  string    `gorm:"type:varchar(50);not null" json:"operation_type"`
    TargetType     string    `gorm:"type:varchar(50);not null" json:"target_type"`
    TargetKSUID    string    `gorm:"type:varchar(32);not null;index" json:"target_ksuid"`
    Description    string    `gorm:"type:text" json:"description"`
    BeforeData     string    `gorm:"type:jsonb" json:"before_data"`
    AfterData      string    `gorm:"type:jsonb" json:"after_data"`
    IPAddress      string    `gorm:"type:varchar(45)" json:"ip_address"`
    UserAgent      string    `gorm:"type:text" json:"user_agent"`
    CreatedAt      time.Time `json:"created_at"`
}
```

### 3.3 内容缓存模型
```go
// ContentCache 内容缓存模型
type ContentCache struct {
    ID            uint      `gorm:"primaryKey" json:"id"`
    ContentKSUID  string    `gorm:"type:varchar(32);not null;uniqueIndex" json:"content_ksuid"`
    ContentType   string    `gorm:"type:varchar(20);not null;index" json:"content_type"`
    CacheData     string    `gorm:"type:jsonb;not null" json:"cache_data"`
    ExpiresAt     time.Time `gorm:"not null;index" json:"expires_at"`
    CreatedAt     time.Time `json:"created_at"`
    UpdatedAt     time.Time `json:"updated_at"`
}
```

## 4. API接口设计

### 4.1 外部API接口

#### 4.1.1 内容管理接口
```
GET    /api/v1/management/contents              # 获取内容列表（支持跨类型查询）
GET    /api/v1/management/contents/:ksuid       # 获取单个内容详情
PUT    /api/v1/management/contents/:ksuid       # 更新内容信息
DELETE /api/v1/management/contents/:ksuid       # 删除内容
POST   /api/v1/management/contents/batch        # 批量操作内容

GET    /api/v1/management/users/:user_ksuid/contents  # 获取用户的所有内容
PUT    /api/v1/management/users/:user_ksuid/contents/status  # 批量更新用户内容状态
```

#### 4.1.2 统计分析接口
```
GET    /api/v1/management/stats/overview         # 获取总体统计概览
GET    /api/v1/management/stats/content-types    # 获取各内容类型统计
GET    /api/v1/management/stats/users            # 获取用户统计
GET    /api/v1/management/stats/trends           # 获取趋势分析
```

#### 4.1.3 操作日志接口
```
GET    /api/v1/management/logs                   # 获取操作日志列表
GET    /api/v1/management/logs/:id               # 获取操作日志详情
```

### 4.2 内部API接口
```
POST   /api/v1/intra/management/sync             # 同步内容数据
POST   /api/v1/intra/management/cache/refresh    # 刷新缓存
GET    /api/v1/intra/management/health           # 健康检查
```

## 5. 服务客户端设计

### 5.1 视频服务客户端接口
```go
// VideoServiceClient 视频服务客户端接口
type VideoServiceClient interface {
    // 内容查询
    GetContentByKSUID(contentKSUID string) (*VideoContent, error)
    GetUserContents(userKSUID string, filters *ContentFilters) (*VideoContentList, error)
    GetContentsByStatus(status string, filters *ContentFilters) (*VideoContentList, error)
    BatchGetContentsByKSUIDs(contentKSUIDs []string) (map[string]*VideoContent, error)
    
    // 内容操作
    UpdateContentStatus(contentKSUID string, status string) error
    DeleteContent(contentKSUID string) error
    BatchUpdateStatus(contentKSUIDs []string, status string) error
    BatchDeleteContents(contentKSUIDs []string) error
    
    // 统计查询
    GetContentStats(contentKSUID string) (*VideoContentStats, error)
    GetUserStats(userKSUID string) (*VideoUserStats, error)
    GetOverallStats() (*VideoOverallStats, error)
}
```

### 5.2 基础客户端实现
```go
// BaseClient 基础HTTP客户端
type BaseClient struct {
    httpClient *http.Client
    baseURL    string
    timeout    time.Duration
    logger     *zerolog.Logger
}

// NewBaseClient 创建基础客户端
func NewBaseClient(baseURL string, timeout time.Duration) *BaseClient {
    return &BaseClient{
        httpClient: &http.Client{
            Timeout: timeout,
        },
        baseURL: baseURL,
        timeout: timeout,
        logger:  &log.Logger,
    }
}

// Get 发送GET请求
func (c *BaseClient) Get(endpoint string, result interface{}) error {
    url := c.baseURL + endpoint
    
    req, err := http.NewRequest("GET", url, nil)
    if err != nil {
        return err
    }
    
    resp, err := c.httpClient.Do(req)
    if err != nil {
        return err
    }
    defer resp.Body.Close()
    
    if resp.StatusCode != http.StatusOK {
        return fmt.Errorf("HTTP %d: %s", resp.StatusCode, resp.Status)
    }
    
    return json.NewDecoder(resp.Body).Decode(result)
}

// Post 发送POST请求
func (c *BaseClient) Post(endpoint string, data interface{}, result interface{}) error {
    // 实现POST请求逻辑
}

// Put 发送PUT请求
func (c *BaseClient) Put(endpoint string, data interface{}, result interface{}) error {
    // 实现PUT请求逻辑
}

// Delete 发送DELETE请求
func (c *BaseClient) Delete(endpoint string) error {
    // 实现DELETE请求逻辑
}
```

## 6. 业务服务设计

### 6.1 内容管理服务
```go
// ContentManagementService 内容管理服务
type ContentManagementService struct {
    videoClient     VideoServiceClient
    novelClient     NovelServiceClient  // 预留
    musicClient     MusicServiceClient  // 预留
    cacheRepo       repository.ContentCacheRepository
    operationRepo   repository.OperationLogRepository
    redis          *redis.Client
    logger         *zerolog.Logger
}

// GetUnifiedContents 获取统一内容列表
func (s *ContentManagementService) GetUnifiedContents(filters *ContentFilters) (*UnifiedContentList, error) {
    var allContents []*UnifiedContent
    var wg sync.WaitGroup
    var mu sync.Mutex

    // 并发获取各服务的内容
    if filters.ContentTypes == nil || contains(filters.ContentTypes, "video") {
        wg.Add(1)
        go func() {
            defer wg.Done()
            videoContents, err := s.videoClient.GetContentsByFilters(filters)
            if err != nil {
                s.logger.Error().Err(err).Msg("Failed to get video contents")
                return
            }

            mu.Lock()
            for _, content := range videoContents.Contents {
                allContents = append(allContents, s.convertVideoToUnified(content))
            }
            mu.Unlock()
        }()
    }

    // 等待所有请求完成
    wg.Wait()

    // 排序和分页
    sortedContents := s.sortContents(allContents, filters.SortBy, filters.SortOrder)
    paginatedContents := s.paginateContents(sortedContents, filters.Page, filters.Limit)

    return &UnifiedContentList{
        Contents:   paginatedContents,
        Total:      len(allContents),
        Page:       filters.Page,
        Limit:      filters.Limit,
        TotalPages: (len(allContents) + filters.Limit - 1) / filters.Limit,
    }, nil
}

// GetUserAllContents 获取用户的所有内容
func (s *ContentManagementService) GetUserAllContents(userKSUID string, filters *ContentFilters) (*UnifiedContentList, error) {
    // 先尝试从缓存获取
    cacheKey := fmt.Sprintf("user_contents:%s:%s", userKSUID, s.generateFilterHash(filters))

    var cachedResult UnifiedContentList
    if err := s.redis.Get(context.Background(), cacheKey).Scan(&cachedResult); err == nil {
        return &cachedResult, nil
    }

    // 缓存未命中，从各服务获取数据
    var allContents []*UnifiedContent
    var wg sync.WaitGroup
    var mu sync.Mutex

    // 获取视频内容
    wg.Add(1)
    go func() {
        defer wg.Done()
        videoContents, err := s.videoClient.GetUserContents(userKSUID, filters)
        if err != nil {
            s.logger.Error().Err(err).Msg("Failed to get user video contents")
            return
        }

        mu.Lock()
        for _, content := range videoContents.Contents {
            allContents = append(allContents, s.convertVideoToUnified(content))
        }
        mu.Unlock()
    }()

    wg.Wait()

    result := &UnifiedContentList{
        Contents: allContents,
        Total:    len(allContents),
    }

    // 缓存结果
    s.redis.Set(context.Background(), cacheKey, result, 5*time.Minute)

    return result, nil
}

// BatchUpdateContentStatus 批量更新内容状态
func (s *ContentManagementService) BatchUpdateContentStatus(operatorKSUID string, request *BatchUpdateStatusRequest) error {
    // 按内容类型分组
    contentsByType := s.groupContentsByType(request.ContentKSUIDs)

    var wg sync.WaitGroup
    var errors []error
    var mu sync.Mutex

    // 并发更新各服务的内容
    for contentType, contentKSUIDs := range contentsByType {
        wg.Add(1)
        go func(cType string, ksUIDs []string) {
            defer wg.Done()

            var err error
            switch cType {
            case "video":
                err = s.videoClient.BatchUpdateStatus(ksUIDs, request.Status)
            case "novel":
                // err = s.novelClient.BatchUpdateStatus(ksUIDs, request.Status)
            }

            if err != nil {
                mu.Lock()
                errors = append(errors, fmt.Errorf("failed to update %s contents: %w", cType, err))
                mu.Unlock()
            }
        }(contentType, contentKSUIDs)
    }

    wg.Wait()

    if len(errors) > 0 {
        return fmt.Errorf("batch update failed: %v", errors)
    }

    // 记录操作日志
    s.logBatchOperation(operatorKSUID, "batch_update_status", request)

    // 清除相关缓存
    s.clearRelatedCache(request.ContentKSUIDs)

    return nil
}
```

### 6.2 统计分析服务
```go
// StatsService 统计分析服务
type StatsService struct {
    videoClient   VideoServiceClient
    novelClient   NovelServiceClient  // 预留
    musicClient   MusicServiceClient  // 预留
    redis        *redis.Client
    logger       *zerolog.Logger
}

// GetOverviewStats 获取总体统计概览
func (s *StatsService) GetOverviewStats() (*OverviewStats, error) {
    cacheKey := "overview_stats"

    // 尝试从缓存获取
    var cachedStats OverviewStats
    if err := s.redis.Get(context.Background(), cacheKey).Scan(&cachedStats); err == nil {
        return &cachedStats, nil
    }

    // 并发获取各服务统计
    var wg sync.WaitGroup
    var mu sync.Mutex

    stats := &OverviewStats{
        LastUpdated: time.Now(),
    }

    // 获取视频统计
    wg.Add(1)
    go func() {
        defer wg.Done()
        videoStats, err := s.videoClient.GetOverallStats()
        if err != nil {
            s.logger.Error().Err(err).Msg("Failed to get video stats")
            return
        }

        mu.Lock()
        stats.TotalContents += videoStats.TotalContents
        stats.TotalViews += videoStats.TotalViews
        stats.TotalLikes += videoStats.TotalLikes
        stats.ContentTypeStats["video"] = &ContentTypeStats{
            Type:         "video",
            Count:        videoStats.TotalContents,
            Views:        videoStats.TotalViews,
            Likes:        videoStats.TotalLikes,
            Comments:     videoStats.TotalComments,
        }
        mu.Unlock()
    }()

    wg.Wait()

    // 缓存结果
    s.redis.Set(context.Background(), cacheKey, stats, 10*time.Minute)

    return stats, nil
}

// GetContentTypeTrends 获取内容类型趋势
func (s *StatsService) GetContentTypeTrends(days int) (*TrendsStats, error) {
    var wg sync.WaitGroup
    var mu sync.Mutex

    trends := &TrendsStats{
        Days:   days,
        Trends: make(map[string][]*DailyStats),
    }

    // 获取视频趋势
    wg.Add(1)
    go func() {
        defer wg.Done()
        videoTrends, err := s.videoClient.GetTrendStats(days)
        if err != nil {
            s.logger.Error().Err(err).Msg("Failed to get video trends")
            return
        }

        mu.Lock()
        trends.Trends["video"] = videoTrends
        mu.Unlock()
    }()

    wg.Wait()

    return trends, nil
}
```

## 7. 数据传输对象(DTO)设计

### 7.1 请求DTO
```go
// ContentFilters 内容过滤器
type ContentFilters struct {
    ContentTypes []string  `json:"content_types,omitempty"` // video, novel, music
    Status       []string  `json:"status,omitempty"`        // draft, published, archived
    UserKSUID    string    `json:"user_ksuid,omitempty"`
    CategoryID   *uint     `json:"category_id,omitempty"`
    Tags         []string  `json:"tags,omitempty"`

    // 时间范围
    CreatedAfter  *time.Time `json:"created_after,omitempty"`
    CreatedBefore *time.Time `json:"created_before,omitempty"`

    // 排序和分页
    SortBy    string `json:"sort_by,omitempty"`    // created_at, updated_at, view_count, like_count
    SortOrder string `json:"sort_order,omitempty"` // asc, desc
    Page      int    `json:"page,omitempty"`
    Limit     int    `json:"limit,omitempty"`

    // 搜索
    Keyword string `json:"keyword,omitempty"`
}

// BatchUpdateStatusRequest 批量更新状态请求
type BatchUpdateStatusRequest struct {
    ContentKSUIDs []string `json:"content_ksuids" binding:"required,min=1"`
    Status        string   `json:"status" binding:"required,oneof=draft published archived deleted"`
    Reason        string   `json:"reason,omitempty"`
}

// BatchDeleteRequest 批量删除请求
type BatchDeleteRequest struct {
    ContentKSUIDs []string `json:"content_ksuids" binding:"required,min=1"`
    Reason        string   `json:"reason,omitempty"`
    Force         bool     `json:"force,omitempty"` // 是否强制删除
}
```

### 7.2 响应DTO
```go
// UnifiedContentList 统一内容列表响应
type UnifiedContentList struct {
    Contents   []*UnifiedContent `json:"contents"`
    Total      int               `json:"total"`
    Page       int               `json:"page"`
    Limit      int               `json:"limit"`
    TotalPages int               `json:"total_pages"`
}

// OverviewStats 总体统计响应
type OverviewStats struct {
    TotalContents     int64                        `json:"total_contents"`
    TotalViews        int64                        `json:"total_views"`
    TotalLikes        int64                        `json:"total_likes"`
    TotalComments     int64                        `json:"total_comments"`
    TotalUsers        int64                        `json:"total_users"`
    ContentTypeStats  map[string]*ContentTypeStats `json:"content_type_stats"`
    LastUpdated       time.Time                    `json:"last_updated"`
}

// ContentTypeStats 内容类型统计
type ContentTypeStats struct {
    Type         string `json:"type"`
    Count        int64  `json:"count"`
    Views        int64  `json:"views"`
    Likes        int64  `json:"likes"`
    Comments     int64  `json:"comments"`
    GrowthRate   float64 `json:"growth_rate"`   // 增长率
}

// TrendsStats 趋势统计响应
type TrendsStats struct {
    Days   int                        `json:"days"`
    Trends map[string][]*DailyStats   `json:"trends"`
}

// DailyStats 每日统计
type DailyStats struct {
    Date     string `json:"date"`
    Contents int64  `json:"contents"`
    Views    int64  `json:"views"`
    Likes    int64  `json:"likes"`
    Comments int64  `json:"comments"`
}
```

## 8. 错误处理设计

### 8.1 错误类型定义
```go
// ManagementError 管理服务错误
type ManagementError struct {
    Code    string `json:"code"`
    Message string `json:"message"`
    Details string `json:"details,omitempty"`
}

func (e *ManagementError) Error() string {
    return fmt.Sprintf("[%s] %s: %s", e.Code, e.Message, e.Details)
}

// 预定义错误
var (
    ErrContentNotFound     = &ManagementError{Code: "CONTENT_NOT_FOUND", Message: "Content not found"}
    ErrInvalidContentType  = &ManagementError{Code: "INVALID_CONTENT_TYPE", Message: "Invalid content type"}
    ErrServiceUnavailable  = &ManagementError{Code: "SERVICE_UNAVAILABLE", Message: "Content service unavailable"}
    ErrPermissionDenied    = &ManagementError{Code: "PERMISSION_DENIED", Message: "Permission denied"}
    ErrBatchOperationFailed = &ManagementError{Code: "BATCH_OPERATION_FAILED", Message: "Batch operation failed"}
)
```

### 8.2 错误处理中间件
```go
// ErrorHandler 错误处理中间件
func ErrorHandler() gin.HandlerFunc {
    return func(c *gin.Context) {
        c.Next()

        if len(c.Errors) > 0 {
            err := c.Errors.Last().Err

            var managementErr *ManagementError
            if errors.As(err, &managementErr) {
                c.JSON(http.StatusBadRequest, gin.H{
                    "error": managementErr,
                })
                return
            }

            // 处理其他类型错误
            c.JSON(http.StatusInternalServerError, gin.H{
                "error": &ManagementError{
                    Code:    "INTERNAL_ERROR",
                    Message: "Internal server error",
                    Details: err.Error(),
                },
            })
        }
    }
}
```

## 9. 缓存策略设计

### 9.1 缓存键设计
```go
const (
    // 内容缓存键前缀
    ContentCachePrefix     = "content_mgmt:content:"
    UserContentCachePrefix = "content_mgmt:user_content:"
    StatsCachePrefix       = "content_mgmt:stats:"

    // 缓存过期时间
    ContentCacheTTL     = 5 * time.Minute
    UserContentCacheTTL = 3 * time.Minute
    StatsCacheTTL       = 10 * time.Minute
)

// CacheKeyBuilder 缓存键构建器
type CacheKeyBuilder struct{}

func (b *CacheKeyBuilder) ContentKey(contentKSUID string) string {
    return fmt.Sprintf("%s%s", ContentCachePrefix, contentKSUID)
}

func (b *CacheKeyBuilder) UserContentKey(userKSUID string, filters *ContentFilters) string {
    filterHash := b.generateFilterHash(filters)
    return fmt.Sprintf("%s%s:%s", UserContentCachePrefix, userKSUID, filterHash)
}

func (b *CacheKeyBuilder) StatsKey(statsType string) string {
    return fmt.Sprintf("%s%s", StatsCachePrefix, statsType)
}
```

### 9.2 缓存管理器
```go
// CacheManager 缓存管理器
type CacheManager struct {
    redis   *redis.Client
    keyBuilder *CacheKeyBuilder
    logger  *zerolog.Logger
}

// GetContent 获取内容缓存
func (m *CacheManager) GetContent(contentKSUID string) (*UnifiedContent, error) {
    key := m.keyBuilder.ContentKey(contentKSUID)

    var content UnifiedContent
    err := m.redis.Get(context.Background(), key).Scan(&content)
    if err != nil {
        return nil, err
    }

    return &content, nil
}

// SetContent 设置内容缓存
func (m *CacheManager) SetContent(content *UnifiedContent) error {
    key := m.keyBuilder.ContentKey(content.ContentKSUID)

    return m.redis.Set(context.Background(), key, content, ContentCacheTTL).Err()
}

// InvalidateContent 失效内容缓存
func (m *CacheManager) InvalidateContent(contentKSUID string) error {
    key := m.keyBuilder.ContentKey(contentKSUID)
    return m.redis.Del(context.Background(), key).Err()
}

// InvalidateUserContent 失效用户内容缓存
func (m *CacheManager) InvalidateUserContent(userKSUID string) error {
    pattern := fmt.Sprintf("%s%s:*", UserContentCachePrefix, userKSUID)

    keys, err := m.redis.Keys(context.Background(), pattern).Result()
    if err != nil {
        return err
    }

    if len(keys) > 0 {
        return m.redis.Del(context.Background(), keys...).Err()
    }

    return nil
}
```

## 10. 权限控制设计

### 10.1 权限级别定义
```go
// Permission 权限类型
type Permission string

const (
    // 内容管理权限
    PermissionViewAllContent    Permission = "content:view:all"
    PermissionViewOwnContent    Permission = "content:view:own"
    PermissionEditAllContent    Permission = "content:edit:all"
    PermissionEditOwnContent    Permission = "content:edit:own"
    PermissionDeleteAllContent  Permission = "content:delete:all"
    PermissionDeleteOwnContent  Permission = "content:delete:own"

    // 统计权限
    PermissionViewStats         Permission = "stats:view"
    PermissionViewDetailedStats Permission = "stats:view:detailed"

    // 批量操作权限
    PermissionBatchOperation    Permission = "batch:operation"
)

// Role 角色定义
type Role string

const (
    RoleAdmin       Role = "admin"        // 管理员：所有权限
    RoleModerator   Role = "moderator"    // 版主：部分管理权限
    RoleCreator     Role = "creator"      // 创作者：自己内容的管理权限
    RoleUser        Role = "user"         // 普通用户：查看权限
)
```

### 10.2 权限检查中间件
```go
// PermissionMiddleware 权限检查中间件
func PermissionMiddleware(requiredPermission Permission) gin.HandlerFunc {
    return func(c *gin.Context) {
        userKSUID, exists := c.Get("user_ksuid")
        if !exists {
            c.JSON(http.StatusUnauthorized, gin.H{
                "error": ErrPermissionDenied,
            })
            c.Abort()
            return
        }

        userRole, exists := c.Get("user_role")
        if !exists {
            c.JSON(http.StatusForbidden, gin.H{
                "error": ErrPermissionDenied,
            })
            c.Abort()
            return
        }

        if !hasPermission(userRole.(Role), requiredPermission) {
            c.JSON(http.StatusForbidden, gin.H{
                "error": ErrPermissionDenied,
            })
            c.Abort()
            return
        }

        c.Next()
    }
}

// hasPermission 检查角色是否有指定权限
func hasPermission(role Role, permission Permission) bool {
    rolePermissions := map[Role][]Permission{
        RoleAdmin: {
            PermissionViewAllContent, PermissionEditAllContent, PermissionDeleteAllContent,
            PermissionViewStats, PermissionViewDetailedStats, PermissionBatchOperation,
        },
        RoleModerator: {
            PermissionViewAllContent, PermissionEditAllContent,
            PermissionViewStats, PermissionBatchOperation,
        },
        RoleCreator: {
            PermissionViewOwnContent, PermissionEditOwnContent, PermissionDeleteOwnContent,
        },
        RoleUser: {
            PermissionViewOwnContent,
        },
    }

    permissions, exists := rolePermissions[role]
    if !exists {
        return false
    }

    for _, p := range permissions {
        if p == permission {
            return true
        }
    }

    return false
}
```

## 11. 配置设计

### 11.1 配置结构
```go
// Config 服务配置
type Config struct {
    Server   ServerConfig   `mapstructure:"server"`
    Database DatabaseConfig `mapstructure:"database"`
    Redis    RedisConfig    `mapstructure:"redis"`
    Services ServicesConfig `mapstructure:"services"`
    Cache    CacheConfig    `mapstructure:"cache"`
    Log      LogConfig      `mapstructure:"log"`
    JWT      JWTConfig      `mapstructure:"jwt"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
    Port        int    `mapstructure:"port"`
    ServiceName string `mapstructure:"service_name"`
    Mode        string `mapstructure:"mode"`
}

// ServicesConfig 外部服务配置
type ServicesConfig struct {
    VideoService VideoServiceConfig `mapstructure:"video_service"`
    NovelService NovelServiceConfig `mapstructure:"novel_service"`
    MusicService MusicServiceConfig `mapstructure:"music_service"`
}

// VideoServiceConfig 视频服务配置
type VideoServiceConfig struct {
    BaseURL string        `mapstructure:"base_url"`
    Timeout time.Duration `mapstructure:"timeout"`
    Enabled bool          `mapstructure:"enabled"`
}

// CacheConfig 缓存配置
type CacheConfig struct {
    ContentTTL     time.Duration `mapstructure:"content_ttl"`
    UserContentTTL time.Duration `mapstructure:"user_content_ttl"`
    StatsTTL       time.Duration `mapstructure:"stats_ttl"`
}
```

### 11.2 配置文件示例
```yaml
# content-management-service配置文件
server:
  port: 12010
  service_name: "content-management-service"
  mode: "debug"

database:
  host: "localhost"
  port: 5432
  user: "postgres"
  password: "postgres"
  dbname: "pxpat"
  ssl_mode: "disable"
  max_open_conns: 10
  max_idle_conns: 5
  conn_max_lifetime_minutes: "60m"
  auto_migrate: true
  time_zone: "Asia/Shanghai"
  log_level: "debug"

redis:
  host: "localhost"
  port: 6379
  password: ""
  db: 5  # 使用独立的数据库
  pool_size: 10
  min_idle_conns: 5

services:
  video_service:
    base_url: "http://localhost:12001"
    timeout: "10s"
    enabled: true
  novel_service:
    base_url: "http://localhost:12002"
    timeout: "10s"
    enabled: false  # 暂时禁用
  music_service:
    base_url: "http://localhost:12003"
    timeout: "10s"
    enabled: false  # 暂时禁用

cache:
  content_ttl: "5m"
  user_content_ttl: "3m"
  stats_ttl: "10m"

jwt:
  secret: "your-secret-key"
  expiration: "168h"

log:
  level: "debug"
  max_size: 100
  max_backups: 3
  max_age: 28
  compress: true
```

## 12. 部署和运维

### 12.1 Docker配置
```dockerfile
# Dockerfile
FROM golang:1.21-alpine AS builder

WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN CGO_ENABLED=0 GOOS=linux go build -o content-management-service ./cmd/content-cluster/content-management-service

FROM alpine:latest
RUN apk --no-cache add ca-certificates tzdata
WORKDIR /root/

COPY --from=builder /app/content-management-service .
COPY --from=builder /app/configs ./configs

EXPOSE 12010

CMD ["./content-management-service"]
```

### 12.2 健康检查
```go
// HealthHandler 健康检查处理器
type HealthHandler struct {
    videoClient VideoServiceClient
    db         *gorm.DB
    redis      *redis.Client
}

// Check 健康检查
func (h *HealthHandler) Check(c *gin.Context) {
    status := gin.H{
        "service": "content-management-service",
        "status":  "healthy",
        "timestamp": time.Now().Unix(),
        "dependencies": gin.H{},
    }

    // 检查数据库连接
    if sqlDB, err := h.db.DB(); err != nil || sqlDB.Ping() != nil {
        status["status"] = "unhealthy"
        status["dependencies"].(gin.H)["database"] = "unhealthy"
    } else {
        status["dependencies"].(gin.H)["database"] = "healthy"
    }

    // 检查Redis连接
    if err := h.redis.Ping(context.Background()).Err(); err != nil {
        status["status"] = "unhealthy"
        status["dependencies"].(gin.H)["redis"] = "unhealthy"
    } else {
        status["dependencies"].(gin.H)["redis"] = "healthy"
    }

    // 检查视频服务连接
    if _, err := h.videoClient.GetOverallStats(); err != nil {
        status["dependencies"].(gin.H)["video_service"] = "unhealthy"
    } else {
        status["dependencies"].(gin.H)["video_service"] = "healthy"
    }

    httpStatus := http.StatusOK
    if status["status"] == "unhealthy" {
        httpStatus = http.StatusServiceUnavailable
    }

    c.JSON(httpStatus, status)
}
```

# 内容管理服务设计开发文档

## 1. 项目概述

### 1.1 服务定位
内容管理服务（content-management-service）是一个独立的微服务，专门负责跨内容类型的统一管理功能。该服务通过API调用的方式与各内容服务（如video-service）进行交互，为管理员和内容创作者提供统一的内容管理界面。

### 1.2 核心职责
- 跨内容类型的统一查询和管理
- 批量内容操作（状态更新、删除等）
- 内容统计分析和报表
- 权限控制和审核流程管理
- 内容生命周期管理

### 1.3 技术架构
- **架构模式**: 微服务架构，基于API调用
- **通信方式**: HTTP REST API
- **数据存储**: PostgreSQL（管理元数据和操作日志）
- **缓存**: Redis（提升查询性能）
- **认证**: JWT Token
- **框架**: Gin + GORM

## 2. 服务架构设计

### 2.1 目录结构
```
internal/content-cluster/content-management-service/
├── client/                          # 外部服务客户端
│   ├── video_service_client.go      # 视频服务客户端
│   ├── novel_service_client.go      # 小说服务客户端（预留）
│   ├── music_service_client.go      # 音乐服务客户端（预留）
│   └── base_client.go               # 基础客户端封装
├── dto/                             # 数据传输对象
│   ├── content_dto.go               # 内容相关DTO
│   ├── management_dto.go            # 管理相关DTO
│   ├── stats_dto.go                 # 统计相关DTO
│   └── common_dto.go                # 通用DTO
├── external/                        # 外部API层
│   ├── handler/                     # 外部API处理器
│   │   ├── content_handler.go       # 内容管理处理器
│   │   ├── stats_handler.go         # 统计分析处理器
│   │   └── batch_handler.go         # 批量操作处理器
│   └── service/                     # 外部业务服务
│       ├── content_service.go       # 内容管理服务
│       ├── stats_service.go         # 统计分析服务
│       └── batch_service.go         # 批量操作服务
├── intra/                           # 内部API层
│   ├── handler/                     # 内部API处理器
│   └── service/                     # 内部业务服务
├── middleware/                      # 中间件
│   ├── auth.go                      # 认证中间件
│   ├── permission.go                # 权限中间件
│   └── rate_limit.go                # 限流中间件
├── model/                           # 数据模型
│   ├── operation_log.go             # 操作日志模型
│   ├── content_cache.go             # 内容缓存模型
│   └── management_config.go         # 管理配置模型
├── repository/                      # 数据访问层
│   ├── operation_log_repo.go        # 操作日志仓库
│   ├── content_cache_repo.go        # 内容缓存仓库
│   └── config_repo.go               # 配置仓库
├── types/                           # 类型定义
│   ├── content_types.go             # 内容类型定义
│   ├── management_types.go          # 管理类型定义
│   └── error_types.go               # 错误类型定义
├── utils/                           # 工具函数
│   ├── aggregator.go                # 数据聚合工具
│   ├── validator.go                 # 验证工具
│   └── converter.go                 # 转换工具
├── config/                          # 配置
│   └── config.go                    # 配置结构
├── migrations/                      # 数据库迁移
│   └── migrate.go                   # 迁移脚本
└── routes/                          # 路由定义
    ├── router.go                    # 主路由
    ├── content/                     # 内容管理路由
    │   ├── router.go
    │   ├── external.go
    │   └── internal.go
    └── stats/                       # 统计分析路由
        ├── router.go
        └── external.go
```

### 2.2 服务端口分配
- **HTTP端口**: 12010
- **gRPC端口**: 22010（预留）
- **健康检查**: `/health`

## 3. 数据模型设计

### 3.1 基础内容模型（重新设计）

#### 3.1.1 核心基础模型
```go
// BaseContent 基础内容模型（只包含所有内容类型都有的字段）
type BaseContent struct {
    ContentKSUID    string    `json:"content_ksuid"`
    ContentType     string    `json:"content_type"`     // video, novel, music
    Title          string    `json:"title"`
    Description    string    `json:"description"`
    UserKSUID      string    `json:"user_ksuid"`
    Status         string    `json:"status"`           // draft, published, archived, deleted

    // 基础统计（所有内容类型都有）
    ViewCount      int64     `json:"view_count"`
    LikeCount      int64     `json:"like_count"`
    CommentCount   int64     `json:"comment_count"`
    FavoriteCount  int64     `json:"favorite_count"`

    // 分类和标签
    CategoryID     uint      `json:"category_id"`
    CategoryName   string    `json:"category_name"`
    Tags           []string  `json:"tags"`

    // 时间信息
    CreatedAt      time.Time `json:"created_at"`
    UpdatedAt      time.Time `json:"updated_at"`
    PublishedAt    *time.Time `json:"published_at,omitempty"`

    // 来源信息
    SourceService  string    `json:"source_service"`   // video-service, novel-service
}

// ContentWithDetails 带详细信息的内容模型
type ContentWithDetails struct {
    BaseContent
    Details interface{} `json:"details"` // 具体内容类型的详细信息
}
```

#### 3.1.2 具体内容类型的详细模型
```go
// VideoDetails 视频特有的详细信息
type VideoDetails struct {
    Duration     float64 `json:"duration"`              // 时长（秒）
    Resolution   string  `json:"resolution"`            // 分辨率
    FileSize     int64   `json:"file_size"`             // 文件大小
    Format       string  `json:"format"`                // 视频格式
    Orientation  string  `json:"orientation"`           // 横屏/竖屏
    Language     string  `json:"language"`              // 语言

    // 播放相关
    PlayURL      string  `json:"play_url"`
    CoverURL     string  `json:"cover_url"`
    PreviewURL   string  `json:"preview_url"`
    KeyFramesURL string  `json:"key_frames_url"`

    // 制作信息
    VideoID      string  `json:"video_id"`
    Director     string  `json:"director"`
    Actors       []string `json:"actors"`

    // 审核信息
    AuditTaskID  uint64  `json:"audit_task_id"`
    Level        string  `json:"level"`                 // A/B/C级别
}

// NovelDetails 小说特有的详细信息
type NovelDetails struct {
    ChapterCount    int64   `json:"chapter_count"`        // 章节数
    WordCount       int64   `json:"word_count"`           // 总字数
    IsCompleted     bool    `json:"is_completed"`         // 是否完结
    SerializationStatus string `json:"serialization_status"` // 连载状态

    // 作者信息
    AuthorKSUID     string  `json:"author_ksuid"`
    Translator      string  `json:"translator,omitempty"`
    OriginalWork    string  `json:"original_work,omitempty"`

    // 分级和定价
    Rating          string  `json:"rating"`               // general, teen, mature
    IsPaid          bool    `json:"is_paid"`              // 是否付费
    Price           float64 `json:"price,omitempty"`      // 价格

    // 封面和简介
    CoverURL        string  `json:"cover_url"`
    Synopsis        string  `json:"synopsis"`             // 作品简介

    // 统计信息
    SubscriberCount int64   `json:"subscriber_count"`     // 订阅数
    RewardCount     int64   `json:"reward_count"`         // 打赏数
    AverageRating   float64 `json:"average_rating"`       // 平均评分
}

// MusicDetails 音乐特有的详细信息（预留）
type MusicDetails struct {
    Duration        float64 `json:"duration"`             // 时长（秒）
    BitRate         int     `json:"bit_rate"`             // 比特率
    SampleRate      int     `json:"sample_rate"`          // 采样率
    Quality         string  `json:"quality"`              // 音质等级
    Format          string  `json:"format"`               // 音频格式
    FileSize        int64   `json:"file_size"`            // 文件大小

    // 音乐信息
    Artist          string  `json:"artist"`               // 艺术家
    Album           string  `json:"album"`                // 专辑
    Genre           string  `json:"genre"`                // 流派
    ReleaseYear     int     `json:"release_year"`         // 发行年份

    // 播放相关
    PlayURL         string  `json:"play_url"`
    CoverURL        string  `json:"cover_url"`
    LyricsURL       string  `json:"lyrics_url,omitempty"` // 歌词文件

    // 统计信息
    PlayCount       int64   `json:"play_count"`           // 播放次数
    DownloadCount   int64   `json:"download_count"`       // 下载次数
}
```

#### 3.1.3 内容类型工厂和转换器
```go
// ContentDetailsFactory 内容详情工厂
type ContentDetailsFactory struct{}

// CreateDetails 根据内容类型创建对应的详情结构
func (f *ContentDetailsFactory) CreateDetails(contentType string) interface{} {
    switch contentType {
    case "video":
        return &VideoDetails{}
    case "novel":
        return &NovelDetails{}
    case "music":
        return &MusicDetails{}
    default:
        return map[string]interface{}{}
    }
}

// ContentConverter 内容转换器
type ContentConverter struct{}

// ConvertVideoToBase 将视频内容转换为基础内容
func (c *ContentConverter) ConvertVideoToBase(video *VideoContent) *BaseContent {
    return &BaseContent{
        ContentKSUID:  video.ContentKSUID,
        ContentType:   "video",
        Title:        video.Title,
        Description:  video.Description,
        UserKSUID:    video.UserKSUID,
        Status:       video.Status,
        ViewCount:    video.ViewCount,
        LikeCount:    video.LikeCount,
        CommentCount: video.CommentCount,
        FavoriteCount: video.FavoriteCount,
        CategoryID:   video.CategoryID,
        CategoryName: video.Category.Name,
        Tags:         c.extractTagNames(video.Tags),
        CreatedAt:    video.CreatedAt,
        UpdatedAt:    video.UpdatedAt,
        PublishedAt:  video.PublishedAt,
        SourceService: "video-service",
    }
}

// ConvertVideoToDetails 将视频内容转换为带详情的内容
func (c *ContentConverter) ConvertVideoToDetails(video *VideoContent) *ContentWithDetails {
    base := c.ConvertVideoToBase(video)

    details := &VideoDetails{
        Duration:     video.Duration,
        Resolution:   video.Resolution,
        FileSize:     video.FileSize,
        Format:       video.Format,
        Orientation:  video.Orientation,
        Language:     video.Language,
        PlayURL:      video.PlayURL,
        CoverURL:     video.CoverURL,
        PreviewURL:   video.PreviewURL,
        KeyFramesURL: video.KeyFramesURL,
        VideoID:      video.VideoID,
        Director:     video.Director,
        Actors:       video.Actors,
        AuditTaskID:  video.AuditTaskID,
        Level:        video.Level,
    }

    return &ContentWithDetails{
        BaseContent: *base,
        Details:     details,
    }
}

// ConvertNovelToBase 将小说内容转换为基础内容
func (c *ContentConverter) ConvertNovelToBase(novel *NovelContent) *BaseContent {
    return &BaseContent{
        ContentKSUID:  novel.NovelKSUID,
        ContentType:   "novel",
        Title:        novel.Title,
        Description:  novel.Description,
        UserKSUID:    novel.UserKSUID,
        Status:       novel.Status,
        ViewCount:    novel.ViewCount,
        LikeCount:    novel.LikeCount,
        CommentCount: novel.CommentCount,
        FavoriteCount: novel.FavoriteCount,
        CategoryID:   novel.CategoryID,
        CategoryName: novel.Category.Name,
        Tags:         c.extractTagNames(novel.Tags),
        CreatedAt:    novel.CreatedAt,
        UpdatedAt:    novel.UpdatedAt,
        PublishedAt:  novel.PublishedAt,
        SourceService: "novel-service",
    }
}

// ConvertNovelToDetails 将小说内容转换为带详情的内容
func (c *ContentConverter) ConvertNovelToDetails(novel *NovelContent) *ContentWithDetails {
    base := c.ConvertNovelToBase(novel)

    details := &NovelDetails{
        ChapterCount:        novel.ChapterCount,
        WordCount:          novel.WordCount,
        IsCompleted:        novel.Status == "completed",
        SerializationStatus: novel.SerializationStatus,
        AuthorKSUID:        novel.AuthorKSUID,
        Translator:         novel.Translator,
        OriginalWork:       novel.OriginalWork,
        Rating:             novel.Rating,
        IsPaid:             novel.IsPaid,
        Price:              novel.Price,
        CoverURL:           novel.CoverURL,
        Synopsis:           novel.Synopsis,
        SubscriberCount:    novel.SubscriberCount,
        RewardCount:        novel.RewardCount,
        AverageRating:      novel.AverageRating,
    }

    return &ContentWithDetails{
        BaseContent: *base,
        Details:     details,
    }
}

// extractTagNames 提取标签名称
func (c *ContentConverter) extractTagNames(tags []Tag) []string {
    names := make([]string, len(tags))
    for i, tag := range tags {
        names[i] = tag.Name
    }
    return names
}
```

#### 3.1.4 设计优势说明

**问题解决方案**：
1. **字段差异问题**：不同内容类型的特有字段放在Details中，避免了统一模型的字段冗余
2. **类型安全**：每种内容类型都有自己的Details结构，保证了类型安全
3. **扩展性**：新增内容类型只需要添加新的Details结构和转换器方法
4. **灵活性**：可以根据需要返回基础信息或详细信息

**使用场景**：
- **列表页面**：使用BaseContent，只显示通用信息，性能更好
- **详情页面**：使用ContentWithDetails，显示完整信息
- **类型特定管理**：直接使用原始的VideoContent、NovelContent等

**API设计策略**：
```go
// 场景1: 跨类型内容列表（管理后台首页）
GET /api/v1/management/contents
// 返回: BaseContentList - 只包含通用字段，性能好

// 场景2: 单个内容详情（内容详情页）
GET /api/v1/management/contents/:ksuid
// 返回: ContentWithDetails - 包含完整信息

// 场景3: 特定类型管理（视频管理页面）
GET /api/v1/management/videos
// 返回: VideoContentList - 视频服务的原始数据结构
```
```

### 3.2 操作日志模型
```go
// OperationLog 操作日志模型
type OperationLog struct {
    ID             uint      `gorm:"primaryKey" json:"id"`
    OperatorKSUID  string    `gorm:"type:varchar(32);not null;index" json:"operator_ksuid"`
    OperationType  string    `gorm:"type:varchar(50);not null" json:"operation_type"`
    TargetType     string    `gorm:"type:varchar(50);not null" json:"target_type"`
    TargetKSUID    string    `gorm:"type:varchar(32);not null;index" json:"target_ksuid"`
    Description    string    `gorm:"type:text" json:"description"`
    BeforeData     string    `gorm:"type:jsonb" json:"before_data"`
    AfterData      string    `gorm:"type:jsonb" json:"after_data"`
    IPAddress      string    `gorm:"type:varchar(45)" json:"ip_address"`
    UserAgent      string    `gorm:"type:text" json:"user_agent"`
    CreatedAt      time.Time `json:"created_at"`
}
```

### 3.3 内容缓存模型
```go
// ContentCache 内容缓存模型
type ContentCache struct {
    ID            uint      `gorm:"primaryKey" json:"id"`
    ContentKSUID  string    `gorm:"type:varchar(32);not null;uniqueIndex" json:"content_ksuid"`
    ContentType   string    `gorm:"type:varchar(20);not null;index" json:"content_type"`
    CacheData     string    `gorm:"type:jsonb;not null" json:"cache_data"`
    ExpiresAt     time.Time `gorm:"not null;index" json:"expires_at"`
    CreatedAt     time.Time `json:"created_at"`
    UpdatedAt     time.Time `json:"updated_at"`
}
```

## 4. API接口设计

### 4.1 外部API接口

#### 4.1.1 内容管理接口
```
# 基础内容管理（只返回通用字段）
GET    /api/v1/management/contents              # 获取内容列表（跨类型，基础信息）
GET    /api/v1/management/contents/:ksuid       # 获取单个内容详情（带完整信息）
PUT    /api/v1/management/contents/:ksuid       # 更新内容信息
DELETE /api/v1/management/contents/:ksuid       # 删除内容
POST   /api/v1/management/contents/batch        # 批量操作内容

# 用户内容管理
GET    /api/v1/management/users/:user_ksuid/contents           # 获取用户的所有内容（基础信息）
GET    /api/v1/management/users/:user_ksuid/contents/:type     # 获取用户指定类型内容（详细信息）
PUT    /api/v1/management/users/:user_ksuid/contents/status    # 批量更新用户内容状态

# 按类型管理（返回该类型的详细信息）
GET    /api/v1/management/videos                # 获取视频列表（带视频特有字段）
GET    /api/v1/management/novels                # 获取小说列表（带小说特有字段）
GET    /api/v1/management/music                 # 获取音乐列表（带音乐特有字段）
```

#### 4.1.2 统计分析接口
```
GET    /api/v1/management/stats/overview         # 获取总体统计概览
GET    /api/v1/management/stats/content-types    # 获取各内容类型统计
GET    /api/v1/management/stats/users            # 获取用户统计
GET    /api/v1/management/stats/trends           # 获取趋势分析

# 按类型的详细统计
GET    /api/v1/management/stats/videos           # 视频统计（包含时长、分辨率等）
GET    /api/v1/management/stats/novels           # 小说统计（包含章节数、字数等）
GET    /api/v1/management/stats/music            # 音乐统计（包含时长、音质等）
```

#### 4.1.3 操作日志接口
```
GET    /api/v1/management/logs                   # 获取操作日志列表
GET    /api/v1/management/logs/:id               # 获取操作日志详情
```

### 4.2 内部API接口
```
POST   /api/v1/intra/management/sync             # 同步内容数据
POST   /api/v1/intra/management/cache/refresh    # 刷新缓存
GET    /api/v1/intra/management/health           # 健康检查
```

## 5. 服务客户端设计

### 5.1 视频服务客户端接口
```go
// VideoServiceClient 视频服务客户端接口
type VideoServiceClient interface {
    // 内容查询
    GetContentByKSUID(contentKSUID string) (*VideoContent, error)
    GetUserContents(userKSUID string, filters *ContentFilters) (*VideoContentList, error)
    GetContentsByStatus(status string, filters *ContentFilters) (*VideoContentList, error)
    BatchGetContentsByKSUIDs(contentKSUIDs []string) (map[string]*VideoContent, error)
    
    // 内容操作
    UpdateContentStatus(contentKSUID string, status string) error
    DeleteContent(contentKSUID string) error
    BatchUpdateStatus(contentKSUIDs []string, status string) error
    BatchDeleteContents(contentKSUIDs []string) error
    
    // 统计查询
    GetContentStats(contentKSUID string) (*VideoContentStats, error)
    GetUserStats(userKSUID string) (*VideoUserStats, error)
    GetOverallStats() (*VideoOverallStats, error)
}
```

### 5.2 基础客户端实现
```go
// BaseClient 基础HTTP客户端
type BaseClient struct {
    httpClient *http.Client
    baseURL    string
    timeout    time.Duration
    logger     *zerolog.Logger
}

// NewBaseClient 创建基础客户端
func NewBaseClient(baseURL string, timeout time.Duration) *BaseClient {
    return &BaseClient{
        httpClient: &http.Client{
            Timeout: timeout,
        },
        baseURL: baseURL,
        timeout: timeout,
        logger:  &log.Logger,
    }
}

// Get 发送GET请求
func (c *BaseClient) Get(endpoint string, result interface{}) error {
    url := c.baseURL + endpoint
    
    req, err := http.NewRequest("GET", url, nil)
    if err != nil {
        return err
    }
    
    resp, err := c.httpClient.Do(req)
    if err != nil {
        return err
    }
    defer resp.Body.Close()
    
    if resp.StatusCode != http.StatusOK {
        return fmt.Errorf("HTTP %d: %s", resp.StatusCode, resp.Status)
    }
    
    return json.NewDecoder(resp.Body).Decode(result)
}

// Post 发送POST请求
func (c *BaseClient) Post(endpoint string, data interface{}, result interface{}) error {
    // 实现POST请求逻辑
}

// Put 发送PUT请求
func (c *BaseClient) Put(endpoint string, data interface{}, result interface{}) error {
    // 实现PUT请求逻辑
}

// Delete 发送DELETE请求
func (c *BaseClient) Delete(endpoint string) error {
    // 实现DELETE请求逻辑
}
```

## 6. 业务服务设计

### 6.1 内容管理服务
```go
// ContentManagementService 内容管理服务
type ContentManagementService struct {
    videoClient     VideoServiceClient
    novelClient     NovelServiceClient  // 预留
    musicClient     MusicServiceClient  // 预留
    cacheRepo       repository.ContentCacheRepository
    operationRepo   repository.OperationLogRepository
    redis          *redis.Client
    logger         *zerolog.Logger
}

// GetBaseContents 获取基础内容列表（只包含通用字段）
func (s *ContentManagementService) GetBaseContents(filters *ContentFilters) (*BaseContentList, error) {
    var allContents []*BaseContent
    var wg sync.WaitGroup
    var mu sync.Mutex

    converter := &ContentConverter{}

    // 并发获取各服务的内容
    if filters.ContentTypes == nil || contains(filters.ContentTypes, "video") {
        wg.Add(1)
        go func() {
            defer wg.Done()
            videoContents, err := s.videoClient.GetContentsByFilters(filters)
            if err != nil {
                s.logger.Error().Err(err).Msg("Failed to get video contents")
                return
            }

            mu.Lock()
            for _, content := range videoContents.Contents {
                allContents = append(allContents, converter.ConvertVideoToBase(content))
            }
            mu.Unlock()
        }()
    }

    // 如果启用了小说服务
    if s.novelClient != nil && (filters.ContentTypes == nil || contains(filters.ContentTypes, "novel")) {
        wg.Add(1)
        go func() {
            defer wg.Done()
            novelContents, err := s.novelClient.GetContentsByFilters(filters)
            if err != nil {
                s.logger.Error().Err(err).Msg("Failed to get novel contents")
                return
            }

            mu.Lock()
            for _, content := range novelContents.Contents {
                allContents = append(allContents, converter.ConvertNovelToBase(content))
            }
            mu.Unlock()
        }()
    }

    // 等待所有请求完成
    wg.Wait()

    // 排序和分页
    sortedContents := s.sortBaseContents(allContents, filters.SortBy, filters.SortOrder)
    paginatedContents := s.paginateBaseContents(sortedContents, filters.Page, filters.Limit)

    return &BaseContentList{
        Contents:   paginatedContents,
        Total:      len(allContents),
        Page:       filters.Page,
        Limit:      filters.Limit,
        TotalPages: (len(allContents) + filters.Limit - 1) / filters.Limit,
    }, nil
}

// GetContentWithDetails 获取带详细信息的内容
func (s *ContentManagementService) GetContentWithDetails(contentKSUID string) (*ContentWithDetails, error) {
    // 先从缓存获取内容类型
    contentType, err := s.getContentType(contentKSUID)
    if err != nil {
        return nil, err
    }

    converter := &ContentConverter{}

    switch contentType {
    case "video":
        videoContent, err := s.videoClient.GetContentByKSUID(contentKSUID)
        if err != nil {
            return nil, err
        }
        return converter.ConvertVideoToDetails(videoContent), nil

    case "novel":
        if s.novelClient == nil {
            return nil, ErrServiceUnavailable
        }
        novelContent, err := s.novelClient.GetContentByKSUID(contentKSUID)
        if err != nil {
            return nil, err
        }
        return converter.ConvertNovelToDetails(novelContent), nil

    default:
        return nil, ErrInvalidContentType
    }
}

// getContentType 获取内容类型（通过KSUID前缀或缓存）
func (s *ContentManagementService) getContentType(contentKSUID string) (string, error) {
    // 方法1: 从缓存获取
    cacheKey := fmt.Sprintf("content_type:%s", contentKSUID)
    contentType, err := s.redis.Get(context.Background(), cacheKey).Result()
    if err == nil {
        return contentType, nil
    }

    // 方法2: 通过KSUID前缀判断（如果有规律的话）
    // 这里需要根据你的KSUID生成规则来实现

    // 方法3: 并发查询各服务（最后的兜底方案）
    return s.detectContentType(contentKSUID)
}

// detectContentType 通过并发查询检测内容类型
func (s *ContentManagementService) detectContentType(contentKSUID string) (string, error) {
    var wg sync.WaitGroup
    var mu sync.Mutex
    var detectedType string

    // 查询视频服务
    wg.Add(1)
    go func() {
        defer wg.Done()
        if _, err := s.videoClient.GetContentByKSUID(contentKSUID); err == nil {
            mu.Lock()
            detectedType = "video"
            mu.Unlock()
        }
    }()

    // 查询小说服务
    if s.novelClient != nil {
        wg.Add(1)
        go func() {
            defer wg.Done()
            if _, err := s.novelClient.GetContentByKSUID(contentKSUID); err == nil {
                mu.Lock()
                if detectedType == "" { // 避免覆盖已检测到的类型
                    detectedType = "novel"
                }
                mu.Unlock()
            }
        }()
    }

    wg.Wait()

    if detectedType == "" {
        return "", ErrContentNotFound
    }

    // 缓存检测结果
    cacheKey := fmt.Sprintf("content_type:%s", contentKSUID)
    s.redis.Set(context.Background(), cacheKey, detectedType, 24*time.Hour)

    return detectedType, nil
}

// GetUserAllContents 获取用户的所有内容（基础信息）
func (s *ContentManagementService) GetUserAllContents(userKSUID string, filters *ContentFilters) (*BaseContentList, error) {
    // 先尝试从缓存获取
    cacheKey := fmt.Sprintf("user_contents:%s:%s", userKSUID, s.generateFilterHash(filters))

    var cachedResult BaseContentList
    if err := s.redis.Get(context.Background(), cacheKey).Scan(&cachedResult); err == nil {
        return &cachedResult, nil
    }

    // 缓存未命中，从各服务获取数据
    var allContents []*BaseContent
    var wg sync.WaitGroup
    var mu sync.Mutex

    converter := &ContentConverter{}

    // 获取视频内容
    wg.Add(1)
    go func() {
        defer wg.Done()
        videoContents, err := s.videoClient.GetUserContents(userKSUID, filters)
        if err != nil {
            s.logger.Error().Err(err).Msg("Failed to get user video contents")
            return
        }

        mu.Lock()
        for _, content := range videoContents.Contents {
            allContents = append(allContents, converter.ConvertVideoToBase(content))
        }
        mu.Unlock()
    }()

    // 获取小说内容
    if s.novelClient != nil {
        wg.Add(1)
        go func() {
            defer wg.Done()
            novelContents, err := s.novelClient.GetUserContents(userKSUID, filters)
            if err != nil {
                s.logger.Error().Err(err).Msg("Failed to get user novel contents")
                return
            }

            mu.Lock()
            for _, content := range novelContents.Contents {
                allContents = append(allContents, converter.ConvertNovelToBase(content))
            }
            mu.Unlock()
        }()
    }

    wg.Wait()

    result := &BaseContentList{
        Contents: allContents,
        Total:    len(allContents),
    }

    // 缓存结果
    s.redis.Set(context.Background(), cacheKey, result, 5*time.Minute)

    return result, nil
}

// GetUserContentsByType 获取用户指定类型的内容（带详细信息）
func (s *ContentManagementService) GetUserContentsByType(userKSUID string, contentType string, filters *ContentFilters) (*ContentWithDetailsList, error) {
    converter := &ContentConverter{}

    switch contentType {
    case "video":
        videoContents, err := s.videoClient.GetUserContents(userKSUID, filters)
        if err != nil {
            return nil, err
        }

        var detailsContents []*ContentWithDetails
        for _, content := range videoContents.Contents {
            detailsContents = append(detailsContents, converter.ConvertVideoToDetails(content))
        }

        return &ContentWithDetailsList{
            Contents:   detailsContents,
            Total:      len(detailsContents),
            ContentType: contentType,
        }, nil

    case "novel":
        if s.novelClient == nil {
            return nil, ErrServiceUnavailable
        }

        novelContents, err := s.novelClient.GetUserContents(userKSUID, filters)
        if err != nil {
            return nil, err
        }

        var detailsContents []*ContentWithDetails
        for _, content := range novelContents.Contents {
            detailsContents = append(detailsContents, converter.ConvertNovelToDetails(content))
        }

        return &ContentWithDetailsList{
            Contents:   detailsContents,
            Total:      len(detailsContents),
            ContentType: contentType,
        }, nil

    default:
        return nil, ErrInvalidContentType
    }
}

// BatchUpdateContentStatus 批量更新内容状态
func (s *ContentManagementService) BatchUpdateContentStatus(operatorKSUID string, request *BatchUpdateStatusRequest) error {
    // 按内容类型分组
    contentsByType, err := s.groupContentsByType(request.ContentKSUIDs)
    if err != nil {
        return err
    }

    var wg sync.WaitGroup
    var errors []error
    var mu sync.Mutex

    // 并发更新各服务的内容
    for contentType, contentKSUIDs := range contentsByType {
        wg.Add(1)
        go func(cType string, ksUIDs []string) {
            defer wg.Done()

            var err error
            switch cType {
            case "video":
                err = s.videoClient.BatchUpdateStatus(ksUIDs, request.Status)
            case "novel":
                if s.novelClient != nil {
                    err = s.novelClient.BatchUpdateStatus(ksUIDs, request.Status)
                } else {
                    err = ErrServiceUnavailable
                }
            case "music":
                if s.musicClient != nil {
                    err = s.musicClient.BatchUpdateStatus(ksUIDs, request.Status)
                } else {
                    err = ErrServiceUnavailable
                }
            }

            if err != nil {
                mu.Lock()
                errors = append(errors, fmt.Errorf("failed to update %s contents: %w", cType, err))
                mu.Unlock()
            }
        }(contentType, contentKSUIDs)
    }

    wg.Wait()

    if len(errors) > 0 {
        return fmt.Errorf("batch update failed: %v", errors)
    }

    // 记录操作日志
    s.logBatchOperation(operatorKSUID, "batch_update_status", request)

    // 清除相关缓存
    s.clearRelatedCache(request.ContentKSUIDs)

    return nil
}

// groupContentsByType 按内容类型分组（需要先检测每个内容的类型）
func (s *ContentManagementService) groupContentsByType(contentKSUIDs []string) (map[string][]string, error) {
    contentsByType := make(map[string][]string)

    // 并发检测每个内容的类型
    var wg sync.WaitGroup
    var mu sync.Mutex
    var errors []error

    for _, contentKSUID := range contentKSUIDs {
        wg.Add(1)
        go func(ksuid string) {
            defer wg.Done()

            contentType, err := s.getContentType(ksuid)
            if err != nil {
                mu.Lock()
                errors = append(errors, fmt.Errorf("failed to detect type for %s: %w", ksuid, err))
                mu.Unlock()
                return
            }

            mu.Lock()
            contentsByType[contentType] = append(contentsByType[contentType], ksuid)
            mu.Unlock()
        }(contentKSUID)
    }

    wg.Wait()

    if len(errors) > 0 {
        return nil, fmt.Errorf("failed to group contents by type: %v", errors)
    }

    return contentsByType, nil
}
```

### 6.3 API处理器示例
```go
// ContentHandler 内容管理处理器
type ContentHandler struct {
    contentService *ContentManagementService
    logger        *zerolog.Logger
}

// GetContents 获取内容列表（基础信息）
func (h *ContentHandler) GetContents(c *gin.Context) {
    // 解析查询参数
    var filters ContentFilters
    if err := c.ShouldBindQuery(&filters); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }

    // 设置默认值
    if filters.Page <= 0 {
        filters.Page = 1
    }
    if filters.Limit <= 0 {
        filters.Limit = 20
    }

    // 获取内容列表
    contents, err := h.contentService.GetBaseContents(&filters)
    if err != nil {
        h.logger.Error().Err(err).Msg("Failed to get contents")
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get contents"})
        return
    }

    c.JSON(http.StatusOK, gin.H{
        "code": 200,
        "data": contents,
    })
}

// GetContentDetails 获取内容详情（完整信息）
func (h *ContentHandler) GetContentDetails(c *gin.Context) {
    contentKSUID := c.Param("ksuid")
    if contentKSUID == "" {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Content KSUID is required"})
        return
    }

    // 获取内容详情
    content, err := h.contentService.GetContentWithDetails(contentKSUID)
    if err != nil {
        if errors.Is(err, ErrContentNotFound) {
            c.JSON(http.StatusNotFound, gin.H{"error": "Content not found"})
            return
        }

        h.logger.Error().Err(err).Str("content_ksuid", contentKSUID).Msg("Failed to get content details")
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get content details"})
        return
    }

    c.JSON(http.StatusOK, gin.H{
        "code": 200,
        "data": content,
    })
}

// GetVideoContents 获取视频列表（视频特有信息）
func (h *ContentHandler) GetVideoContents(c *gin.Context) {
    // 解析查询参数
    var filters ContentFilters
    if err := c.ShouldBindQuery(&filters); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }

    // 强制设置内容类型为视频
    filters.ContentTypes = []string{"video"}

    // 直接调用视频服务获取完整的视频信息
    videoContents, err := h.contentService.videoClient.GetContentsByFilters(&filters)
    if err != nil {
        h.logger.Error().Err(err).Msg("Failed to get video contents")
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get video contents"})
        return
    }

    c.JSON(http.StatusOK, gin.H{
        "code": 200,
        "data": videoContents,
    })
}

// GetUserContentsByType 获取用户指定类型的内容
func (h *ContentHandler) GetUserContentsByType(c *gin.Context) {
    userKSUID := c.Param("user_ksuid")
    contentType := c.Param("type")

    if userKSUID == "" || contentType == "" {
        c.JSON(http.StatusBadRequest, gin.H{"error": "User KSUID and content type are required"})
        return
    }

    // 验证内容类型
    validTypes := []string{"video", "novel", "music"}
    if !contains(validTypes, contentType) {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid content type"})
        return
    }

    // 解析查询参数
    var filters ContentFilters
    if err := c.ShouldBindQuery(&filters); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }

    // 获取用户指定类型的内容
    contents, err := h.contentService.GetUserContentsByType(userKSUID, contentType, &filters)
    if err != nil {
        if errors.Is(err, ErrServiceUnavailable) {
            c.JSON(http.StatusServiceUnavailable, gin.H{"error": fmt.Sprintf("%s service is not available", contentType)})
            return
        }

        h.logger.Error().Err(err).
            Str("user_ksuid", userKSUID).
            Str("content_type", contentType).
            Msg("Failed to get user contents by type")
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user contents"})
        return
    }

    c.JSON(http.StatusOK, gin.H{
        "code": 200,
        "data": contents,
    })
}

// BatchUpdateStatus 批量更新内容状态
func (h *ContentHandler) BatchUpdateStatus(c *gin.Context) {
    // 获取操作者信息
    operatorKSUID, exists := c.Get("user_ksuid")
    if !exists {
        c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
        return
    }

    // 解析请求体
    var request BatchUpdateStatusRequest
    if err := c.ShouldBindJSON(&request); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }

    // 执行批量更新
    err := h.contentService.BatchUpdateContentStatus(operatorKSUID.(string), &request)
    if err != nil {
        h.logger.Error().Err(err).
            Str("operator", operatorKSUID.(string)).
            Interface("request", request).
            Msg("Failed to batch update content status")
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update content status"})
        return
    }

    c.JSON(http.StatusOK, gin.H{
        "code": 200,
        "message": "Content status updated successfully",
    })
}

// contains 检查切片是否包含指定元素
func contains(slice []string, item string) bool {
    for _, s := range slice {
        if s == item {
            return true
        }
    }
    return false
}
```

### 6.2 统计分析服务
```go
// StatsService 统计分析服务
type StatsService struct {
    videoClient   VideoServiceClient
    novelClient   NovelServiceClient  // 预留
    musicClient   MusicServiceClient  // 预留
    redis        *redis.Client
    logger       *zerolog.Logger
}

// GetOverviewStats 获取总体统计概览
func (s *StatsService) GetOverviewStats() (*OverviewStats, error) {
    cacheKey := "overview_stats"

    // 尝试从缓存获取
    var cachedStats OverviewStats
    if err := s.redis.Get(context.Background(), cacheKey).Scan(&cachedStats); err == nil {
        return &cachedStats, nil
    }

    // 并发获取各服务统计
    var wg sync.WaitGroup
    var mu sync.Mutex

    stats := &OverviewStats{
        LastUpdated: time.Now(),
    }

    // 获取视频统计
    wg.Add(1)
    go func() {
        defer wg.Done()
        videoStats, err := s.videoClient.GetOverallStats()
        if err != nil {
            s.logger.Error().Err(err).Msg("Failed to get video stats")
            return
        }

        mu.Lock()
        stats.TotalContents += videoStats.TotalContents
        stats.TotalViews += videoStats.TotalViews
        stats.TotalLikes += videoStats.TotalLikes
        stats.ContentTypeStats["video"] = &ContentTypeStats{
            Type:         "video",
            Count:        videoStats.TotalContents,
            Views:        videoStats.TotalViews,
            Likes:        videoStats.TotalLikes,
            Comments:     videoStats.TotalComments,
        }
        mu.Unlock()
    }()

    wg.Wait()

    // 缓存结果
    s.redis.Set(context.Background(), cacheKey, stats, 10*time.Minute)

    return stats, nil
}

// GetContentTypeTrends 获取内容类型趋势
func (s *StatsService) GetContentTypeTrends(days int) (*TrendsStats, error) {
    var wg sync.WaitGroup
    var mu sync.Mutex

    trends := &TrendsStats{
        Days:   days,
        Trends: make(map[string][]*DailyStats),
    }

    // 获取视频趋势
    wg.Add(1)
    go func() {
        defer wg.Done()
        videoTrends, err := s.videoClient.GetTrendStats(days)
        if err != nil {
            s.logger.Error().Err(err).Msg("Failed to get video trends")
            return
        }

        mu.Lock()
        trends.Trends["video"] = videoTrends
        mu.Unlock()
    }()

    wg.Wait()

    return trends, nil
}
```

## 7. 数据传输对象(DTO)设计

### 7.1 请求DTO
```go
// ContentFilters 内容过滤器
type ContentFilters struct {
    ContentTypes []string  `json:"content_types,omitempty"` // video, novel, music
    Status       []string  `json:"status,omitempty"`        // draft, published, archived
    UserKSUID    string    `json:"user_ksuid,omitempty"`
    CategoryID   *uint     `json:"category_id,omitempty"`
    Tags         []string  `json:"tags,omitempty"`

    // 时间范围
    CreatedAfter  *time.Time `json:"created_after,omitempty"`
    CreatedBefore *time.Time `json:"created_before,omitempty"`

    // 排序和分页
    SortBy    string `json:"sort_by,omitempty"`    // created_at, updated_at, view_count, like_count
    SortOrder string `json:"sort_order,omitempty"` // asc, desc
    Page      int    `json:"page,omitempty"`
    Limit     int    `json:"limit,omitempty"`

    // 搜索
    Keyword string `json:"keyword,omitempty"`
}

// BatchUpdateStatusRequest 批量更新状态请求
type BatchUpdateStatusRequest struct {
    ContentKSUIDs []string `json:"content_ksuids" binding:"required,min=1"`
    Status        string   `json:"status" binding:"required,oneof=draft published archived deleted"`
    Reason        string   `json:"reason,omitempty"`
}

// BatchDeleteRequest 批量删除请求
type BatchDeleteRequest struct {
    ContentKSUIDs []string `json:"content_ksuids" binding:"required,min=1"`
    Reason        string   `json:"reason,omitempty"`
    Force         bool     `json:"force,omitempty"` // 是否强制删除
}
```

### 7.2 响应DTO
```go
// BaseContentList 基础内容列表响应
type BaseContentList struct {
    Contents   []*BaseContent `json:"contents"`
    Total      int            `json:"total"`
    Page       int            `json:"page"`
    Limit      int            `json:"limit"`
    TotalPages int            `json:"total_pages"`
}

// ContentWithDetailsList 带详细信息的内容列表响应
type ContentWithDetailsList struct {
    Contents    []*ContentWithDetails `json:"contents"`
    Total       int                   `json:"total"`
    Page        int                   `json:"page"`
    Limit       int                   `json:"limit"`
    TotalPages  int                   `json:"total_pages"`
    ContentType string                `json:"content_type"` // 当前列表的内容类型
}

// OverviewStats 总体统计响应
type OverviewStats struct {
    TotalContents     int64                        `json:"total_contents"`
    TotalViews        int64                        `json:"total_views"`
    TotalLikes        int64                        `json:"total_likes"`
    TotalComments     int64                        `json:"total_comments"`
    TotalUsers        int64                        `json:"total_users"`
    ContentTypeStats  map[string]*ContentTypeStats `json:"content_type_stats"`
    LastUpdated       time.Time                    `json:"last_updated"`
}

// ContentTypeStats 内容类型统计
type ContentTypeStats struct {
    Type         string `json:"type"`
    Count        int64  `json:"count"`
    Views        int64  `json:"views"`
    Likes        int64  `json:"likes"`
    Comments     int64  `json:"comments"`
    GrowthRate   float64 `json:"growth_rate"`   // 增长率
}

// TrendsStats 趋势统计响应
type TrendsStats struct {
    Days   int                        `json:"days"`
    Trends map[string][]*DailyStats   `json:"trends"`
}

// DailyStats 每日统计
type DailyStats struct {
    Date     string `json:"date"`
    Contents int64  `json:"contents"`
    Views    int64  `json:"views"`
    Likes    int64  `json:"likes"`
    Comments int64  `json:"comments"`
}
```

## 8. 错误处理设计

### 8.1 错误类型定义
```go
// ManagementError 管理服务错误
type ManagementError struct {
    Code    string `json:"code"`
    Message string `json:"message"`
    Details string `json:"details,omitempty"`
}

func (e *ManagementError) Error() string {
    return fmt.Sprintf("[%s] %s: %s", e.Code, e.Message, e.Details)
}

// 预定义错误
var (
    ErrContentNotFound     = &ManagementError{Code: "CONTENT_NOT_FOUND", Message: "Content not found"}
    ErrInvalidContentType  = &ManagementError{Code: "INVALID_CONTENT_TYPE", Message: "Invalid content type"}
    ErrServiceUnavailable  = &ManagementError{Code: "SERVICE_UNAVAILABLE", Message: "Content service unavailable"}
    ErrPermissionDenied    = &ManagementError{Code: "PERMISSION_DENIED", Message: "Permission denied"}
    ErrBatchOperationFailed = &ManagementError{Code: "BATCH_OPERATION_FAILED", Message: "Batch operation failed"}
)
```

### 8.2 错误处理中间件
```go
// ErrorHandler 错误处理中间件
func ErrorHandler() gin.HandlerFunc {
    return func(c *gin.Context) {
        c.Next()

        if len(c.Errors) > 0 {
            err := c.Errors.Last().Err

            var managementErr *ManagementError
            if errors.As(err, &managementErr) {
                c.JSON(http.StatusBadRequest, gin.H{
                    "error": managementErr,
                })
                return
            }

            // 处理其他类型错误
            c.JSON(http.StatusInternalServerError, gin.H{
                "error": &ManagementError{
                    Code:    "INTERNAL_ERROR",
                    Message: "Internal server error",
                    Details: err.Error(),
                },
            })
        }
    }
}
```

## 9. 缓存策略设计

### 9.1 缓存键设计
```go
const (
    // 内容缓存键前缀
    ContentCachePrefix     = "content_mgmt:content:"
    UserContentCachePrefix = "content_mgmt:user_content:"
    StatsCachePrefix       = "content_mgmt:stats:"

    // 缓存过期时间
    ContentCacheTTL     = 5 * time.Minute
    UserContentCacheTTL = 3 * time.Minute
    StatsCacheTTL       = 10 * time.Minute
)

// CacheKeyBuilder 缓存键构建器
type CacheKeyBuilder struct{}

func (b *CacheKeyBuilder) ContentKey(contentKSUID string) string {
    return fmt.Sprintf("%s%s", ContentCachePrefix, contentKSUID)
}

func (b *CacheKeyBuilder) UserContentKey(userKSUID string, filters *ContentFilters) string {
    filterHash := b.generateFilterHash(filters)
    return fmt.Sprintf("%s%s:%s", UserContentCachePrefix, userKSUID, filterHash)
}

func (b *CacheKeyBuilder) StatsKey(statsType string) string {
    return fmt.Sprintf("%s%s", StatsCachePrefix, statsType)
}
```

### 9.2 缓存管理器
```go
// CacheManager 缓存管理器
type CacheManager struct {
    redis   *redis.Client
    keyBuilder *CacheKeyBuilder
    logger  *zerolog.Logger
}

// GetContent 获取内容缓存
func (m *CacheManager) GetContent(contentKSUID string) (*UnifiedContent, error) {
    key := m.keyBuilder.ContentKey(contentKSUID)

    var content UnifiedContent
    err := m.redis.Get(context.Background(), key).Scan(&content)
    if err != nil {
        return nil, err
    }

    return &content, nil
}

// SetContent 设置内容缓存
func (m *CacheManager) SetContent(content *UnifiedContent) error {
    key := m.keyBuilder.ContentKey(content.ContentKSUID)

    return m.redis.Set(context.Background(), key, content, ContentCacheTTL).Err()
}

// InvalidateContent 失效内容缓存
func (m *CacheManager) InvalidateContent(contentKSUID string) error {
    key := m.keyBuilder.ContentKey(contentKSUID)
    return m.redis.Del(context.Background(), key).Err()
}

// InvalidateUserContent 失效用户内容缓存
func (m *CacheManager) InvalidateUserContent(userKSUID string) error {
    pattern := fmt.Sprintf("%s%s:*", UserContentCachePrefix, userKSUID)

    keys, err := m.redis.Keys(context.Background(), pattern).Result()
    if err != nil {
        return err
    }

    if len(keys) > 0 {
        return m.redis.Del(context.Background(), keys...).Err()
    }

    return nil
}
```

## 10. 权限控制设计

### 10.1 权限级别定义
```go
// Permission 权限类型
type Permission string

const (
    // 内容管理权限
    PermissionViewAllContent    Permission = "content:view:all"
    PermissionViewOwnContent    Permission = "content:view:own"
    PermissionEditAllContent    Permission = "content:edit:all"
    PermissionEditOwnContent    Permission = "content:edit:own"
    PermissionDeleteAllContent  Permission = "content:delete:all"
    PermissionDeleteOwnContent  Permission = "content:delete:own"

    // 统计权限
    PermissionViewStats         Permission = "stats:view"
    PermissionViewDetailedStats Permission = "stats:view:detailed"

    // 批量操作权限
    PermissionBatchOperation    Permission = "batch:operation"
)

// Role 角色定义
type Role string

const (
    RoleAdmin       Role = "admin"        // 管理员：所有权限
    RoleModerator   Role = "moderator"    // 版主：部分管理权限
    RoleCreator     Role = "creator"      // 创作者：自己内容的管理权限
    RoleUser        Role = "user"         // 普通用户：查看权限
)
```

### 10.2 权限检查中间件
```go
// PermissionMiddleware 权限检查中间件
func PermissionMiddleware(requiredPermission Permission) gin.HandlerFunc {
    return func(c *gin.Context) {
        userKSUID, exists := c.Get("user_ksuid")
        if !exists {
            c.JSON(http.StatusUnauthorized, gin.H{
                "error": ErrPermissionDenied,
            })
            c.Abort()
            return
        }

        userRole, exists := c.Get("user_role")
        if !exists {
            c.JSON(http.StatusForbidden, gin.H{
                "error": ErrPermissionDenied,
            })
            c.Abort()
            return
        }

        if !hasPermission(userRole.(Role), requiredPermission) {
            c.JSON(http.StatusForbidden, gin.H{
                "error": ErrPermissionDenied,
            })
            c.Abort()
            return
        }

        c.Next()
    }
}

// hasPermission 检查角色是否有指定权限
func hasPermission(role Role, permission Permission) bool {
    rolePermissions := map[Role][]Permission{
        RoleAdmin: {
            PermissionViewAllContent, PermissionEditAllContent, PermissionDeleteAllContent,
            PermissionViewStats, PermissionViewDetailedStats, PermissionBatchOperation,
        },
        RoleModerator: {
            PermissionViewAllContent, PermissionEditAllContent,
            PermissionViewStats, PermissionBatchOperation,
        },
        RoleCreator: {
            PermissionViewOwnContent, PermissionEditOwnContent, PermissionDeleteOwnContent,
        },
        RoleUser: {
            PermissionViewOwnContent,
        },
    }

    permissions, exists := rolePermissions[role]
    if !exists {
        return false
    }

    for _, p := range permissions {
        if p == permission {
            return true
        }
    }

    return false
}
```

## 11. 配置设计

### 11.1 配置结构
```go
// Config 服务配置
type Config struct {
    Server   ServerConfig   `mapstructure:"server"`
    Database DatabaseConfig `mapstructure:"database"`
    Redis    RedisConfig    `mapstructure:"redis"`
    Services ServicesConfig `mapstructure:"services"`
    Cache    CacheConfig    `mapstructure:"cache"`
    Log      LogConfig      `mapstructure:"log"`
    JWT      JWTConfig      `mapstructure:"jwt"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
    Port        int    `mapstructure:"port"`
    ServiceName string `mapstructure:"service_name"`
    Mode        string `mapstructure:"mode"`
}

// ServicesConfig 外部服务配置
type ServicesConfig struct {
    VideoService VideoServiceConfig `mapstructure:"video_service"`
    NovelService NovelServiceConfig `mapstructure:"novel_service"`
    MusicService MusicServiceConfig `mapstructure:"music_service"`
}

// VideoServiceConfig 视频服务配置
type VideoServiceConfig struct {
    BaseURL string        `mapstructure:"base_url"`
    Timeout time.Duration `mapstructure:"timeout"`
    Enabled bool          `mapstructure:"enabled"`
}

// CacheConfig 缓存配置
type CacheConfig struct {
    ContentTTL     time.Duration `mapstructure:"content_ttl"`
    UserContentTTL time.Duration `mapstructure:"user_content_ttl"`
    StatsTTL       time.Duration `mapstructure:"stats_ttl"`
}
```

### 11.2 配置文件示例
```yaml
# content-management-service配置文件
server:
  port: 12010
  service_name: "content-management-service"
  mode: "debug"

database:
  host: "localhost"
  port: 5432
  user: "postgres"
  password: "postgres"
  dbname: "pxpat"
  ssl_mode: "disable"
  max_open_conns: 10
  max_idle_conns: 5
  conn_max_lifetime_minutes: "60m"
  auto_migrate: true
  time_zone: "Asia/Shanghai"
  log_level: "debug"

redis:
  host: "localhost"
  port: 6379
  password: ""
  db: 5  # 使用独立的数据库
  pool_size: 10
  min_idle_conns: 5

services:
  video_service:
    base_url: "http://localhost:12001"
    timeout: "10s"
    enabled: true
  novel_service:
    base_url: "http://localhost:12002"
    timeout: "10s"
    enabled: false  # 暂时禁用
  music_service:
    base_url: "http://localhost:12003"
    timeout: "10s"
    enabled: false  # 暂时禁用

cache:
  content_ttl: "5m"
  user_content_ttl: "3m"
  stats_ttl: "10m"

jwt:
  secret: "your-secret-key"
  expiration: "168h"

log:
  level: "debug"
  max_size: 100
  max_backups: 3
  max_age: 28
  compress: true
```

## 12. 部署和运维

### 12.1 Docker配置
```dockerfile
# Dockerfile
FROM golang:1.21-alpine AS builder

WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN CGO_ENABLED=0 GOOS=linux go build -o content-management-service ./cmd/content-cluster/content-management-service

FROM alpine:latest
RUN apk --no-cache add ca-certificates tzdata
WORKDIR /root/

COPY --from=builder /app/content-management-service .
COPY --from=builder /app/configs ./configs

EXPOSE 12010

CMD ["./content-management-service"]
```

### 12.2 健康检查
```go
// HealthHandler 健康检查处理器
type HealthHandler struct {
    videoClient VideoServiceClient
    db         *gorm.DB
    redis      *redis.Client
}

// Check 健康检查
func (h *HealthHandler) Check(c *gin.Context) {
    status := gin.H{
        "service": "content-management-service",
        "status":  "healthy",
        "timestamp": time.Now().Unix(),
        "dependencies": gin.H{},
    }

    // 检查数据库连接
    if sqlDB, err := h.db.DB(); err != nil || sqlDB.Ping() != nil {
        status["status"] = "unhealthy"
        status["dependencies"].(gin.H)["database"] = "unhealthy"
    } else {
        status["dependencies"].(gin.H)["database"] = "healthy"
    }

    // 检查Redis连接
    if err := h.redis.Ping(context.Background()).Err(); err != nil {
        status["status"] = "unhealthy"
        status["dependencies"].(gin.H)["redis"] = "unhealthy"
    } else {
        status["dependencies"].(gin.H)["redis"] = "healthy"
    }

    // 检查视频服务连接
    if _, err := h.videoClient.GetOverallStats(); err != nil {
        status["dependencies"].(gin.H)["video_service"] = "unhealthy"
    } else {
        status["dependencies"].(gin.H)["video_service"] = "healthy"
    }

    httpStatus := http.StatusOK
    if status["status"] == "unhealthy" {
        httpStatus = http.StatusServiceUnavailable
    }

    c.JSON(httpStatus, status)
}
```

## 13. 开发实施步骤

### 13.1 第一阶段：基础框架搭建（1-2天）
1. **创建项目结构**
   - 按照设计的目录结构创建文件夹
   - 初始化Go模块和基础依赖

2. **配置管理**
   - 实现配置结构和加载逻辑
   - 创建开发环境配置文件

3. **基础设施**
   - 数据库连接和迁移
   - Redis连接
   - 日志系统初始化

4. **健康检查**
   - 实现基础的健康检查接口

### 13.2 第二阶段：视频服务客户端（2-3天）
1. **基础HTTP客户端**
   - 实现BaseClient的完整功能
   - 添加重试机制和超时处理
   - 实现请求/响应日志

2. **视频服务客户端**
   - 实现VideoServiceClient接口
   - 添加所有必需的API调用方法
   - 实现错误处理和类型转换

3. **客户端测试**
   - 编写单元测试
   - 集成测试验证与video-service的连接

### 13.3 第三阶段：核心业务逻辑（3-4天）
1. **数据模型**
   - 实现所有数据模型
   - 数据库迁移脚本
   - 模型验证和转换函数

2. **业务服务**
   - 实现ContentManagementService
   - 实现StatsService
   - 实现BatchService

3. **缓存管理**
   - 实现CacheManager
   - 缓存策略和失效机制

### 13.4 第四阶段：API接口实现（2-3天）
1. **外部API处理器**
   - 实现所有外部API接口
   - 请求验证和响应格式化
   - 错误处理

2. **权限控制**
   - 实现权限中间件
   - 角色和权限检查逻辑

3. **路由注册**
   - 注册所有API路由
   - 中间件配置

### 13.5 第五阶段：测试和优化（2-3天）
1. **单元测试**
   - 业务逻辑测试
   - 客户端测试
   - 缓存测试

2. **集成测试**
   - 与video-service的集成测试
   - 端到端API测试

3. **性能优化**
   - 并发处理优化
   - 缓存策略调优
   - 数据库查询优化

## 14. 关键技术要点

### 14.1 并发处理
- 使用goroutine并发调用多个服务API
- 使用sync.WaitGroup等待所有请求完成
- 使用sync.Mutex保护共享数据

### 14.2 错误处理
- 统一的错误类型定义
- 服务降级策略（某个服务不可用时的处理）
- 错误日志记录和监控

### 14.3 缓存策略
- 多级缓存（Redis + 内存）
- 缓存预热和失效策略
- 缓存穿透和雪崩防护

### 14.4 性能优化
- 连接池管理
- 请求合并和批处理
- 数据分页和流式处理

## 15. 监控和日志

### 15.1 关键指标监控
- API响应时间
- 服务调用成功率
- 缓存命中率
- 并发请求数

### 15.2 日志记录
- 结构化日志（JSON格式）
- 请求追踪ID
- 错误堆栈信息
- 性能指标记录

## 16. 扩展性考虑

### 16.1 新内容类型支持
- 客户端接口标准化
- 统一的数据转换逻辑
- 配置驱动的服务启用/禁用

### 16.2 功能扩展
- 内容审核工作流
- 批量导入/导出
- 数据分析和报表
- 内容推荐管理

这个设计文档提供了完整的技术实现指导，AI可以根据这个文档逐步实现内容管理服务的各个组件。

## 11. 配置设计

### 11.1 配置结构
```go
// Config 服务配置
type Config struct {
    Server   ServerConfig   `mapstructure:"server"`
    Database DatabaseConfig `mapstructure:"database"`
    Redis    RedisConfig    `mapstructure:"redis"`
    Services ServicesConfig `mapstructure:"services"`
    Cache    CacheConfig    `mapstructure:"cache"`
    Log      LogConfig      `mapstructure:"log"`
    JWT      JWTConfig      `mapstructure:"jwt"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
    Port        int    `mapstructure:"port"`
    ServiceName string `mapstructure:"service_name"`
    Mode        string `mapstructure:"mode"`
}

// ServicesConfig 外部服务配置
type ServicesConfig struct {
    VideoService VideoServiceConfig `mapstructure:"video_service"`
    NovelService NovelServiceConfig `mapstructure:"novel_service"`
    MusicService MusicServiceConfig `mapstructure:"music_service"`
}

// VideoServiceConfig 视频服务配置
type VideoServiceConfig struct {
    BaseURL string        `mapstructure:"base_url"`
    Timeout time.Duration `mapstructure:"timeout"`
    Enabled bool          `mapstructure:"enabled"`
}

// CacheConfig 缓存配置
type CacheConfig struct {
    ContentTTL     time.Duration `mapstructure:"content_ttl"`
    UserContentTTL time.Duration `mapstructure:"user_content_ttl"`
    StatsTTL       time.Duration `mapstructure:"stats_ttl"`
}
```

### 11.2 配置文件示例
```yaml
# content-management-service配置文件
server:
  port: 12010
  service_name: "content-management-service"
  mode: "debug"

database:
  host: "localhost"
  port: 5432
  user: "postgres"
  password: "postgres"
  dbname: "pxpat"
  ssl_mode: "disable"
  max_open_conns: 10
  max_idle_conns: 5
  conn_max_lifetime_minutes: "60m"
  auto_migrate: true
  time_zone: "Asia/Shanghai"
  log_level: "debug"

redis:
  host: "localhost"
  port: 6379
  password: ""
  db: 5  # 使用独立的数据库
  pool_size: 10
  min_idle_conns: 5

services:
  video_service:
    base_url: "http://localhost:12001"
    timeout: "10s"
    enabled: true
  novel_service:
    base_url: "http://localhost:12002"
    timeout: "10s"
    enabled: false  # 暂时禁用
  music_service:
    base_url: "http://localhost:12003"
    timeout: "10s"
    enabled: false  # 暂时禁用

cache:
  content_ttl: "5m"
  user_content_ttl: "3m"
  stats_ttl: "10m"

jwt:
  secret: "your-secret-key"
  expiration: "168h"

log:
  level: "debug"
  max_size: 100
  max_backups: 3
  max_age: 28
  compress: true
```

## 12. 部署和运维

### 12.1 Docker配置
```dockerfile
# Dockerfile
FROM golang:1.21-alpine AS builder

WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN CGO_ENABLED=0 GOOS=linux go build -o content-management-service ./cmd/content-cluster/content-management-service

FROM alpine:latest
RUN apk --no-cache add ca-certificates tzdata
WORKDIR /root/

COPY --from=builder /app/content-management-service .
COPY --from=builder /app/configs ./configs

EXPOSE 12010

CMD ["./content-management-service"]
```

### 12.2 健康检查
```go
// HealthHandler 健康检查处理器
type HealthHandler struct {
    videoClient VideoServiceClient
    db         *gorm.DB
    redis      *redis.Client
}

// Check 健康检查
func (h *HealthHandler) Check(c *gin.Context) {
    status := gin.H{
        "service": "content-management-service",
        "status":  "healthy",
        "timestamp": time.Now().Unix(),
        "dependencies": gin.H{},
    }

    // 检查数据库连接
    if sqlDB, err := h.db.DB(); err != nil || sqlDB.Ping() != nil {
        status["status"] = "unhealthy"
        status["dependencies"].(gin.H)["database"] = "unhealthy"
    } else {
        status["dependencies"].(gin.H)["database"] = "healthy"
    }

    // 检查Redis连接
    if err := h.redis.Ping(context.Background()).Err(); err != nil {
        status["status"] = "unhealthy"
        status["dependencies"].(gin.H)["redis"] = "unhealthy"
    } else {
        status["dependencies"].(gin.H)["redis"] = "healthy"
    }

    // 检查视频服务连接
    if _, err := h.videoClient.GetOverallStats(); err != nil {
        status["dependencies"].(gin.H)["video_service"] = "unhealthy"
    } else {
        status["dependencies"].(gin.H)["video_service"] = "healthy"
    }

    httpStatus := http.StatusOK
    if status["status"] == "unhealthy" {
        httpStatus = http.StatusServiceUnavailable
    }

    c.JSON(httpStatus, status)
}
```

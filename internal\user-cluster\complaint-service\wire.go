//go:build wireinject
// +build wireinject

package complaint_service

import (
	"context"

	"github.com/google/wire"
	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"

	"pxpat-backend/internal/user-cluster/complaint-service/external/handler"
	"pxpat-backend/internal/user-cluster/complaint-service/external/service"
	"pxpat-backend/internal/user-cluster/complaint-service/repository/impl"
	"pxpat-backend/internal/user-cluster/complaint-service/types"
	"pxpat-backend/pkg/cache"
)

// ProviderSet 依赖注入提供者集合
var ProviderSet = wire.NewSet(
	// Repository层
	impl.NewComplaintRepository,
	impl.NewComplaintEvidenceRepository,
	impl.NewIdentityVerificationRepository,
	impl.NewCountryRepository,
	impl.NewTrademarkCategoryRepository,

	// Service层
	service.NewComplaintService,
	service.NewIdentityService,
	service.NewRightsService,
	NewUserServiceClient,

	// Handler层
	handler.NewComplaintHandler,
	handler.NewIdentityHandler,
	handler.NewRightsHandler,

	// 应用程序
	NewApp,
)

// NewApp 创建应用程序实例
func NewApp(
	complaintHandler *handler.ComplaintHandler,
	identityHandler *handler.IdentityHandler,
	rightsHandler *handler.RightsHandler,
	config *types.Config,
) *App {
	return &App{
		ComplaintHandler: complaintHandler,
		IdentityHandler:  identityHandler,
		RightsHandler:    rightsHandler,
		Config:           config,
	}
}

// App 应用程序结构体
type App struct {
	ComplaintHandler *handler.ComplaintHandler
	IdentityHandler  *handler.IdentityHandler
	RightsHandler    *handler.RightsHandler
	Config           *types.Config
}

// UserServiceClient 用户服务客户端
type UserServiceClient struct {
	config *types.Config
}

// NewUserServiceClient 创建用户服务客户端
func NewUserServiceClient(config *types.Config) service.UserServiceClient {
	return &UserServiceClient{
		config: config,
	}
}

// BlockUser 拉黑用户
func (c *UserServiceClient) BlockUser(ctx context.Context, blockerKSUID, blockedKSUID string) error {
	// TODO: 实现调用用户服务的拉黑接口
	return nil
}

// GetUserInfo 获取用户信息
func (c *UserServiceClient) GetUserInfo(ctx context.Context, userKSUID string) (*service.UserInfo, error) {
	// TODO: 实现调用用户服务获取用户信息接口
	return &service.UserInfo{
		UserKSUID: userKSUID,
		Nickname:  "测试用户",
		Email:     "<EMAIL>",
	}, nil
}

// InitializeApp 初始化应用程序
func InitializeApp(db *gorm.DB, rdb *redis.Client, cacheManager cache.Manager, config *types.Config) (*App, error) {
	wire.Build(ProviderSet)
	return &App{}, nil
}
